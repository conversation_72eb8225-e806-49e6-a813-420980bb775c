#!/usr/bin/env python3
"""
Debug with real data patterns to find the MaxModSharpe issue
"""

import numpy as np
import pandas as pd
from portfolio_optimizer_rust import process_combo_rust, RustPortfolioOptimizer
import ratio_calcs_rust

def test_with_problematic_data():
    """Test with data patterns that might cause MaxModSharpe to fail"""
    print("🔍 Testing with Problematic Data Patterns")
    print("=" * 45)
    
    # Test case 1: Very small returns (might cause numerical issues)
    print("--- Test 1: Very small returns ---")
    test_small_returns()
    
    # Test case 2: High correlation data
    print("\n--- Test 2: High correlation data ---")
    test_high_correlation()
    
    # Test case 3: Extreme skewness/kurtosis
    print("\n--- Test 3: Extreme skewness/kurtosis ---")
    test_extreme_moments()
    
    # Test case 4: Mixed positive/negative returns
    print("\n--- Test 4: Mixed positive/negative returns ---")
    test_mixed_returns()

def test_small_returns():
    """Test with very small return values that might cause numerical issues"""
    np.random.seed(42)
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    periods = 300
    
    # Very small returns - typical of minute-level FX data
    returns_data = {}
    for symbol in symbols:
        returns_data[symbol] = np.random.normal(0.00001, 0.0005, periods)  # Very small
    
    returns_df = pd.DataFrame(returns_data)
    combo = (tuple(symbols), returns_df, returns_df)
    
    print(f"  Mean returns: {returns_df.mean().values}")
    print(f"  Std returns: {returns_df.std().values}")
    
    # Test direct optimization
    mean_returns = returns_df.mean().values
    cov_matrix = returns_df.cov().values
    returns_matrix = returns_df.values.T
    
    try:
        weights, success, obj_value = ratio_calcs_rust.optimize_portfolio_rust(
            mean_returns.astype(np.float64),
            cov_matrix.astype(np.float64),
            returns_matrix.astype(np.float64),
            'max_modified_sharpe',
            np.array([-1.0, -1.0, -1.0]),
            np.array([1.0, 1.0, 1.0]),
            200
        )
        
        print(f"  Direct optimization: Success={success}, Obj={obj_value}")
        if success:
            print(f"  Weights: {weights}")
            print(f"  Min weight: {np.min(np.abs(weights)):.6f}")
    except Exception as e:
        print(f"  ❌ Direct optimization error: {e}")
    
    # Test through pipeline
    results = process_combo_rust([combo])
    maxmod_count = len([r for r in results if r[1].get('optimization') == 'Max CF Sharpe'])
    print(f"  Pipeline results: {maxmod_count} MaxModSharpe portfolios")

def test_high_correlation():
    """Test with highly correlated assets"""
    np.random.seed(100)
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    periods = 300
    
    # Create highly correlated returns
    base_returns = np.random.normal(0.001, 0.015, periods)
    noise_scale = 0.1
    
    returns_data = {}
    returns_data['EURUSD'] = base_returns + np.random.normal(0, 0.001, periods) * noise_scale
    returns_data['GBPUSD'] = base_returns + np.random.normal(0, 0.001, periods) * noise_scale
    returns_data['USDJPY'] = -base_returns + np.random.normal(0, 0.001, periods) * noise_scale  # Negatively correlated
    
    returns_df = pd.DataFrame(returns_data)
    combo = (tuple(symbols), returns_df, returns_df)
    
    # Check correlation
    corr_matrix = returns_df.corr()
    print(f"  Correlation matrix:")
    print(f"    EUR-GBP: {corr_matrix.loc['EURUSD', 'GBPUSD']:.3f}")
    print(f"    EUR-JPY: {corr_matrix.loc['EURUSD', 'USDJPY']:.3f}")
    print(f"    GBP-JPY: {corr_matrix.loc['GBPUSD', 'USDJPY']:.3f}")
    
    results = process_combo_rust([combo])
    maxmod_count = len([r for r in results if r[1].get('optimization') == 'Max CF Sharpe'])
    print(f"  Pipeline results: {maxmod_count} MaxModSharpe portfolios")

def test_extreme_moments():
    """Test with extreme skewness and kurtosis"""
    np.random.seed(200)
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    periods = 300
    
    returns_data = {}
    
    # Create data with extreme skewness (fat tails, outliers)
    for i, symbol in enumerate(symbols):
        # Mix of normal and extreme values
        normal_returns = np.random.normal(0.001, 0.01, int(periods * 0.9))
        extreme_returns = np.random.normal(0.001, 0.05, int(periods * 0.1)) * (3 if i % 2 == 0 else -3)
        all_returns = np.concatenate([normal_returns, extreme_returns])
        np.random.shuffle(all_returns)
        returns_data[symbol] = all_returns[:periods]
    
    returns_df = pd.DataFrame(returns_data)
    combo = (tuple(symbols), returns_df, returns_df)
    
    # Check moments
    from scipy import stats
    for symbol in symbols:
        skew = stats.skew(returns_df[symbol])
        kurt = stats.kurtosis(returns_df[symbol], fisher=True)
        print(f"  {symbol}: Skew={skew:.3f}, Kurt={kurt:.3f}")
    
    results = process_combo_rust([combo])
    maxmod_count = len([r for r in results if r[1].get('optimization') == 'Max CF Sharpe'])
    print(f"  Pipeline results: {maxmod_count} MaxModSharpe portfolios")

def test_mixed_returns():
    """Test with mixed positive/negative expected returns"""
    np.random.seed(300)
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    periods = 300
    
    # Create assets with different expected returns (some negative)
    returns_data = {}
    returns_data['EURUSD'] = np.random.normal(0.002, 0.015, periods)   # Positive
    returns_data['GBPUSD'] = np.random.normal(-0.001, 0.018, periods)  # Negative
    returns_data['USDJPY'] = np.random.normal(0.0005, 0.012, periods)  # Small positive
    
    returns_df = pd.DataFrame(returns_data)
    combo = (tuple(symbols), returns_df, returns_df)
    
    mean_returns = returns_df.mean().values
    print(f"  Mean returns: {mean_returns}")
    print(f"  Has negative: {np.any(mean_returns < 0)}")
    
    results = process_combo_rust([combo])
    maxmod_count = len([r for r in results if r[1].get('optimization') == 'Max CF Sharpe'])
    print(f"  Pipeline results: {maxmod_count} MaxModSharpe portfolios")
    
    # Also test if the portfolio return filter is the issue
    if maxmod_count == 0:
        print("  Testing if negative portfolio return is the issue...")
        
        # Test direct optimization
        mean_returns = returns_df.mean().values
        cov_matrix = returns_df.cov().values
        returns_matrix = returns_df.values.T
        
        try:
            weights, success, obj_value = ratio_calcs_rust.optimize_portfolio_rust(
                mean_returns.astype(np.float64),
                cov_matrix.astype(np.float64),
                returns_matrix.astype(np.float64),
                'max_modified_sharpe',
                np.array([-1.0, -1.0, -1.0]),
                np.array([1.0, 1.0, 1.0]),
                200
            )
            
            if success:
                portfolio_return = np.dot(mean_returns, weights)
                print(f"    Optimized portfolio return: {portfolio_return:.6f}")
                print(f"    Return >= 0: {portfolio_return >= 0}")
                if portfolio_return < 0:
                    print("    ❌ FOUND THE ISSUE: Portfolio return is negative!")
                    print("    💡 MaxModSharpe is being filtered by the return >= 0 check")
        except Exception as e:
            print(f"    Error in direct test: {e}")

def test_exception_handling():
    """Test if exceptions are being silently caught"""
    print("\n🔍 Testing Exception Handling")
    print("=" * 35)
    
    # Test with invalid data that should cause exceptions
    print("--- Test with NaN data ---")
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    periods = 100
    
    returns_data = {}
    for symbol in symbols:
        data = np.random.normal(0.001, 0.015, periods)
        data[50:55] = np.nan  # Insert some NaN values
        returns_data[symbol] = data
    
    returns_df = pd.DataFrame(returns_data)
    combo = (tuple(symbols), returns_df, returns_df)
    
    print(f"  Data has NaN: {returns_df.isnull().any().any()}")
    
    try:
        results = process_combo_rust([combo])
        print(f"  Results: {len(results)} total")
        maxmod_count = len([r for r in results if r[1].get('optimization') == 'Max CF Sharpe'])
        print(f"  MaxModSharpe: {maxmod_count}")
    except Exception as e:
        print(f"  Exception caught: {e}")

if __name__ == "__main__":
    test_with_problematic_data()
    test_exception_handling()
