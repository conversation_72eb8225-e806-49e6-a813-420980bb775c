#!/usr/bin/env python3
"""
Test with lower weight threshold to confirm MaxModSharpe is working
"""

import numpy as np
import pandas as pd
from portfolio_optimizer_rust import process_combo_rust

def test_with_lower_threshold():
    """Test MaxModSharpe with lower weight threshold"""
    print("🔍 Testing with Lower Weight Threshold")
    print("=" * 40)
    
    # Create the problematic data
    np.random.seed(300)
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    periods = 300
    
    returns_data = {}
    returns_data['EURUSD'] = np.random.normal(0.002, 0.015, periods)   # Positive
    returns_data['GBPUSD'] = np.random.normal(-0.001, 0.018, periods)  # Negative
    returns_data['USDJPY'] = np.random.normal(0.0005, 0.012, periods)  # Small positive
    
    returns_df = pd.DataFrame(returns_data)
    combo = (tuple(symbols), returns_df, returns_df)
    
    print(f"Mean returns: {returns_df.mean().values}")
    print()
    
    # Temporarily modify the weight threshold in the code
    print("Testing with original 0.05 threshold:")
    results_005 = process_combo_rust([combo])
    maxmod_count_005 = len([r for r in results_005 if r[1].get('optimization') == 'Max CF Sharpe'])
    print(f"  MaxModSharpe results: {maxmod_count_005}")
    
    # Now let's manually test what would happen with a lower threshold
    print("\nManual test with 0.001 threshold:")
    
    # We know the optimal weights from our previous test
    optimal_weights = np.array([0.6912617857158375, -0.0007433022086457344, 0.3094815164928082])
    
    # Check if it would pass with lower threshold
    abs_weights = np.abs(optimal_weights)
    min_weight = np.min(abs_weights)
    passes_001_threshold = np.all(abs_weights >= 0.001)
    
    print(f"  Optimal weights: {optimal_weights}")
    print(f"  Min absolute weight: {min_weight:.6f}")
    print(f"  Passes 0.001 threshold: {passes_001_threshold}")
    
    if passes_001_threshold:
        # Calculate portfolio metrics
        mean_returns = returns_df.mean().values
        portfolio_return = np.dot(mean_returns, optimal_weights)
        print(f"  Portfolio return: {portfolio_return:.6f}")
        print(f"  Return >= 0: {portfolio_return >= 0}")
        
        if portfolio_return >= 0:
            print(f"  ✅ Would be included with 0.001 threshold!")
        else:
            print(f"  ❌ Would still be filtered by negative return")
    else:
        print(f"  ❌ Would still be filtered by 0.001 threshold")

def compare_optimization_results():
    """Compare results across different optimization types with the same data"""
    print("\n🔍 Comparing All Optimization Types")
    print("=" * 40)
    
    # Use the same problematic data
    np.random.seed(300)
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    periods = 300
    
    returns_data = {}
    returns_data['EURUSD'] = np.random.normal(0.002, 0.015, periods)
    returns_data['GBPUSD'] = np.random.normal(-0.001, 0.018, periods)
    returns_data['USDJPY'] = np.random.normal(0.0005, 0.012, periods)
    
    returns_df = pd.DataFrame(returns_data)
    combo = (tuple(symbols), returns_df, returns_df)
    
    results = process_combo_rust([combo])
    
    print(f"Total results: {len(results)}")
    
    # Analyze each optimization type
    optimization_counts = {}
    optimization_details = {}
    
    for result_type, result_dict in results:
        opt_name = result_dict.get('optimization', 'Unknown')
        optimization_counts[opt_name] = optimization_counts.get(opt_name, 0) + 1
        
        if opt_name not in optimization_details:
            optimization_details[opt_name] = []
        optimization_details[opt_name].append(result_dict)
    
    expected_optimizations = [
        'Min Variance',
        'Max Sharpe', 
        'Max Sortino',
        'Max Omega',
        'Max Calmar',
        'Max CF Sharpe'
    ]
    
    for opt_name in expected_optimizations:
        count = optimization_counts.get(opt_name, 0)
        status = "✅" if count > 0 else "❌"
        print(f"\n{status} {opt_name}: {count} results")
        
        if count > 0 and opt_name in optimization_details:
            # Show details of first result
            first_result = optimization_details[opt_name][0]
            weights = first_result.get('weights', [])
            ret = first_result.get('return', 0)
            risk = first_result.get('risk', 0)
            
            if weights:
                min_weight = min(abs(w) for w in weights)
                print(f"    Sample: Return={ret:.6f}, Risk={risk:.6f}")
                print(f"    Weights: {[f'{w:.4f}' for w in weights]}")
                print(f"    Min |weight|: {min_weight:.6f}")
                print(f"    Passes 0.05: {min_weight >= 0.05}")

def test_with_different_data():
    """Test with different data that might not have the weight threshold issue"""
    print("\n🔍 Testing with Different Data")
    print("=" * 35)
    
    # Create data where all assets have positive expected returns
    np.random.seed(500)
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    periods = 300
    
    returns_data = {}
    returns_data['EURUSD'] = np.random.normal(0.002, 0.015, periods)   # Positive
    returns_data['GBPUSD'] = np.random.normal(0.001, 0.018, periods)   # Positive (changed from negative)
    returns_data['USDJPY'] = np.random.normal(0.0015, 0.012, periods)  # Positive
    
    returns_df = pd.DataFrame(returns_data)
    combo = (tuple(symbols), returns_df, returns_df)
    
    mean_returns = returns_df.mean().values
    print(f"Mean returns: {mean_returns}")
    print(f"All positive: {np.all(mean_returns > 0)}")
    
    results = process_combo_rust([combo])
    
    optimization_counts = {}
    for result_type, result_dict in results:
        opt_name = result_dict.get('optimization', 'Unknown')
        optimization_counts[opt_name] = optimization_counts.get(opt_name, 0) + 1
    
    maxmod_count = optimization_counts.get('Max CF Sharpe', 0)
    print(f"MaxModSharpe results: {maxmod_count}")
    
    if maxmod_count > 0:
        print("✅ MaxModSharpe works with all-positive returns!")
        # Show the result
        maxmod_results = [r for r in results if r[1].get('optimization') == 'Max CF Sharpe']
        for result_type, result_dict in maxmod_results[:1]:
            weights = result_dict.get('weights', [])
            ret = result_dict.get('return', 0)
            risk = result_dict.get('risk', 0)
            min_weight = min(abs(w) for w in weights) if weights else 0
            
            print(f"  Return: {ret:.6f}, Risk: {risk:.6f}")
            print(f"  Weights: {[f'{w:.4f}' for w in weights]}")
            print(f"  Min |weight|: {min_weight:.6f}")
    else:
        print("❌ MaxModSharpe still failing even with all-positive returns!")

if __name__ == "__main__":
    test_with_lower_threshold()
    compare_optimization_results()
    test_with_different_data()
