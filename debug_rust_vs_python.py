#!/usr/bin/env python3
"""
Compare Rust MaxModSharpe implementation with Python reference
"""

import numpy as np
import pandas as pd
import ratio_calcs_rust
from scipy import stats
from scipy.optimize import minimize

def python_modified_sharpe_objective(weights, mean_returns, returns_matrix, threshold=0.0):
    """Python reference implementation of Modified Sharpe ratio"""
    portfolio_return = np.dot(mean_returns, weights)
    
    # Calculate portfolio returns series
    if returns_matrix.shape[0] == len(weights):
        portfolio_series = np.dot(returns_matrix.T, weights)
    else:
        portfolio_series = np.dot(returns_matrix, weights)
    
    # Calculate variance from series (same as Rust)
    portfolio_volatility = np.std(portfolio_series, ddof=1)
    
    if portfolio_volatility < 1e-15:
        return 0.0
    
    # Calculate skewness and kurtosis
    skewness = stats.skew(portfolio_series)
    kurtosis = stats.kurtosis(portfolio_series, fisher=True)  # Excess kurtosis
    
    # Standard normal quantile for 95% confidence level
    z = 1.6448536269514722
    
    # Cornish-Fisher expansion adjustment
    z_mod = z + (1.0/6.0) * (z*z - 1.0) * skewness + \
           (1.0/24.0) * (z**3 - 3.0*z) * kurtosis - \
           (1.0/36.0) * (2.0*z**3 - 5.0*z) * (skewness**2)
    
    # Adjust volatility
    mod_vol = portfolio_volatility * (z_mod / z)
    
    if mod_vol < 1e-15:
        return 0.0
    
    modified_sharpe = (portfolio_return - threshold) / mod_vol
    return -modified_sharpe  # Negative for minimization

def test_rust_vs_python():
    """Test Rust vs Python MaxModSharpe implementations"""
    print("🔍 Comparing Rust vs Python MaxModSharpe")
    print("=" * 45)
    
    # Create test data
    np.random.seed(42)
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    periods = 200
    
    returns_data = {}
    for symbol in symbols:
        returns_data[symbol] = np.random.normal(0.001, 0.015, periods)
    
    returns_df = pd.DataFrame(returns_data)
    mean_returns = returns_df.mean().values
    cov_matrix = returns_df.cov().values
    returns_matrix = returns_df.values.T  # Transpose for Rust (assets x time_periods)
    
    print(f"Test data: {periods} periods, {len(symbols)} assets")
    print(f"Mean returns: {mean_returns}")
    print(f"Returns matrix shape: {returns_matrix.shape}")
    print()
    
    # Test with several different weight combinations
    test_weights = [
        np.array([1/3, 1/3, 1/3]),
        np.array([0.5, 0.3, 0.2]),
        np.array([0.7, 0.1, 0.2]),
        np.array([0.2, 0.6, 0.2]),
    ]
    
    for i, weights in enumerate(test_weights):
        print(f"--- Test {i+1}: Weights {weights} ---")
        
        # Python calculation
        python_obj = python_modified_sharpe_objective(weights, mean_returns, returns_matrix)
        print(f"Python objective: {python_obj:.10f}")
        
        # Manual calculation for verification
        portfolio_return = np.dot(mean_returns, weights)
        portfolio_series = np.dot(returns_matrix.T, weights)
        portfolio_volatility = np.std(portfolio_series, ddof=1)
        skewness = stats.skew(portfolio_series)
        kurtosis = stats.kurtosis(portfolio_series, fisher=True)
        
        z = 1.6448536269514722
        z_mod = z + (1.0/6.0) * (z*z - 1.0) * skewness + \
               (1.0/24.0) * (z**3 - 3.0*z) * kurtosis - \
               (1.0/36.0) * (2.0*z**3 - 5.0*z) * (skewness**2)
        mod_vol = portfolio_volatility * (z_mod / z)
        modified_sharpe = portfolio_return / mod_vol
        
        print(f"  Portfolio return: {portfolio_return:.10f}")
        print(f"  Portfolio volatility: {portfolio_volatility:.10f}")
        print(f"  Skewness: {skewness:.10f}")
        print(f"  Kurtosis: {kurtosis:.10f}")
        print(f"  Z_mod: {z_mod:.10f}")
        print(f"  Modified volatility: {mod_vol:.10f}")
        print(f"  Modified Sharpe: {modified_sharpe:.10f}")
        print(f"  Expected objective: {-modified_sharpe:.10f}")
        print()

def test_rust_optimization_directly():
    """Test the Rust optimization directly and compare with Python optimization"""
    print("🔍 Testing Rust Optimization vs Python Optimization")
    print("=" * 55)
    
    # Create test data
    np.random.seed(100)
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    periods = 150
    
    returns_data = {}
    for symbol in symbols:
        returns_data[symbol] = np.random.normal(0.001, 0.015, periods)
    
    returns_df = pd.DataFrame(returns_data)
    mean_returns = returns_df.mean().values
    cov_matrix = returns_df.cov().values
    returns_matrix = returns_df.values.T
    
    print(f"Test data: {periods} periods, {len(symbols)} assets")
    print(f"Mean returns: {mean_returns}")
    print()
    
    # Python optimization
    print("--- Python Optimization ---")
    initial_weights = np.array([1/3, 1/3, 1/3])
    bounds = [(-1.0, 1.0) for _ in range(len(symbols))]
    constraints = {'type': 'eq', 'fun': lambda w: np.sum(w) - 1.0}
    
    try:
        python_result = minimize(
            python_modified_sharpe_objective,
            initial_weights,
            args=(mean_returns, returns_matrix),
            method='SLSQP',
            bounds=bounds,
            constraints=constraints,
            options={'maxiter': 200}
        )
        
        print(f"Python success: {python_result.success}")
        print(f"Python objective: {python_result.fun:.10f}")
        print(f"Python weights: {python_result.x}")
        print(f"Python weights sum: {np.sum(python_result.x):.10f}")
        
        if python_result.success:
            # Calculate metrics for Python result
            python_weights = python_result.x
            python_portfolio_return = np.dot(mean_returns, python_weights)
            python_portfolio_series = np.dot(returns_matrix.T, python_weights)
            python_modified_sharpe = -python_result.fun
            
            print(f"Python portfolio return: {python_portfolio_return:.10f}")
            print(f"Python Modified Sharpe: {python_modified_sharpe:.10f}")
        
    except Exception as e:
        print(f"Python optimization error: {e}")
    
    print()
    
    # Rust optimization
    print("--- Rust Optimization ---")
    try:
        rust_weights, rust_success, rust_objective = ratio_calcs_rust.optimize_portfolio_rust(
            mean_returns.astype(np.float64),
            cov_matrix.astype(np.float64),
            returns_matrix.astype(np.float64),
            'max_modified_sharpe',
            np.array([-1.0, -1.0, -1.0]),
            np.array([1.0, 1.0, 1.0]),
            200
        )
        
        print(f"Rust success: {rust_success}")
        print(f"Rust objective: {rust_objective:.10f}")
        print(f"Rust weights: {rust_weights}")
        print(f"Rust weights sum: {np.sum(rust_weights):.10f}")
        
        if rust_success:
            # Calculate metrics for Rust result
            rust_portfolio_return = np.dot(mean_returns, rust_weights)
            rust_portfolio_series = np.dot(returns_matrix.T, rust_weights)
            rust_modified_sharpe = -rust_objective
            
            print(f"Rust portfolio return: {rust_portfolio_return:.10f}")
            print(f"Rust Modified Sharpe: {rust_modified_sharpe:.10f}")
            
            # Verify the objective calculation
            manual_objective = python_modified_sharpe_objective(rust_weights, mean_returns, returns_matrix)
            print(f"Manual verification of Rust weights: {manual_objective:.10f}")
            print(f"Objective match: {abs(rust_objective - manual_objective) < 1e-8}")
        
    except Exception as e:
        print(f"Rust optimization error: {e}")
        import traceback
        traceback.print_exc()

def test_edge_cases():
    """Test edge cases that might cause issues"""
    print("\n🔍 Testing Edge Cases")
    print("=" * 25)
    
    # Test case 1: Very small returns
    print("--- Edge Case 1: Very small returns ---")
    test_small_returns_edge()
    
    # Test case 2: High volatility
    print("\n--- Edge Case 2: High volatility ---")
    test_high_volatility_edge()
    
    # Test case 3: Extreme skewness
    print("\n--- Edge Case 3: Extreme skewness ---")
    test_extreme_skewness_edge()

def test_small_returns_edge():
    """Test with very small returns"""
    np.random.seed(200)
    mean_returns = np.array([0.00001, 0.000005, 0.000015])
    returns_matrix = np.random.normal(0.00001, 0.0001, (3, 100))
    cov_matrix = np.cov(returns_matrix)
    
    try:
        weights, success, objective = ratio_calcs_rust.optimize_portfolio_rust(
            mean_returns.astype(np.float64),
            cov_matrix.astype(np.float64),
            returns_matrix.astype(np.float64),
            'max_modified_sharpe',
            np.array([-1.0, -1.0, -1.0]),
            np.array([1.0, 1.0, 1.0]),
            100
        )
        
        print(f"  Success: {success}, Objective: {objective:.10f}")
        if success:
            print(f"  Weights: {weights}")
            
    except Exception as e:
        print(f"  Error: {e}")

def test_high_volatility_edge():
    """Test with high volatility"""
    np.random.seed(300)
    mean_returns = np.array([0.01, 0.005, 0.015])
    returns_matrix = np.random.normal(0.01, 0.1, (3, 100))  # High volatility
    cov_matrix = np.cov(returns_matrix)
    
    try:
        weights, success, objective = ratio_calcs_rust.optimize_portfolio_rust(
            mean_returns.astype(np.float64),
            cov_matrix.astype(np.float64),
            returns_matrix.astype(np.float64),
            'max_modified_sharpe',
            np.array([-1.0, -1.0, -1.0]),
            np.array([1.0, 1.0, 1.0]),
            100
        )
        
        print(f"  Success: {success}, Objective: {objective:.10f}")
        if success:
            print(f"  Weights: {weights}")
            
    except Exception as e:
        print(f"  Error: {e}")

def test_extreme_skewness_edge():
    """Test with extreme skewness"""
    np.random.seed(400)
    mean_returns = np.array([0.001, 0.0005, 0.002])
    
    # Create data with extreme skewness
    normal_data = np.random.normal(0.001, 0.01, (3, 80))
    extreme_data = np.random.normal(0.001, 0.05, (3, 20)) * 5  # Extreme outliers
    returns_matrix = np.concatenate([normal_data, extreme_data], axis=1)
    
    cov_matrix = np.cov(returns_matrix)
    
    try:
        weights, success, objective = ratio_calcs_rust.optimize_portfolio_rust(
            mean_returns.astype(np.float64),
            cov_matrix.astype(np.float64),
            returns_matrix.astype(np.float64),
            'max_modified_sharpe',
            np.array([-1.0, -1.0, -1.0]),
            np.array([1.0, 1.0, 1.0]),
            100
        )
        
        print(f"  Success: {success}, Objective: {objective:.10f}")
        if success:
            print(f"  Weights: {weights}")
            
    except Exception as e:
        print(f"  Error: {e}")

if __name__ == "__main__":
    test_rust_vs_python()
    test_rust_optimization_directly()
    test_edge_cases()
