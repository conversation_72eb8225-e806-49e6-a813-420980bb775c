#!/usr/bin/env python3
"""
Debug the Rust MaxModSharpe implementation to find the error
"""

import numpy as np
import pandas as pd
import ratio_calcs_rust

def test_rust_maxmodsharpe_directly():
    """Test the Rust MaxModSharpe implementation directly"""
    print("🔍 Testing Rust MaxModSharpe Implementation Directly")
    print("=" * 55)
    
    # Create simple test data
    np.random.seed(42)
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    periods = 200
    
    returns_data = {}
    for symbol in symbols:
        returns_data[symbol] = np.random.normal(0.001, 0.015, periods)
    
    returns_df = pd.DataFrame(returns_data)
    mean_returns = returns_df.mean().values
    cov_matrix = returns_df.cov().values
    returns_matrix = returns_df.values.T  # Transpose for Rust (assets x time_periods)
    
    print(f"Test data: {periods} periods, {len(symbols)} assets")
    print(f"Mean returns: {mean_returns}")
    print(f"Returns matrix shape: {returns_matrix.shape}")
    print()
    
    # Test each optimization type
    optimization_types = [
        'min_variance',
        'max_sharpe', 
        'max_sortino',
        'max_omega',
        'max_calmar',
        'max_modified_sharpe'
    ]
    
    bounds_lower = np.array([-1.0, -1.0, -1.0])
    bounds_upper = np.array([1.0, 1.0, 1.0])
    max_iterations = 200
    
    for opt_type in optimization_types:
        print(f"--- Testing {opt_type} ---")
        try:
            weights, success, objective_value = ratio_calcs_rust.optimize_portfolio_rust(
                mean_returns.astype(np.float64),
                cov_matrix.astype(np.float64),
                returns_matrix.astype(np.float64),
                opt_type,
                bounds_lower,
                bounds_upper,
                max_iterations
            )
            
            print(f"  Success: {success}")
            print(f"  Objective: {objective_value}")
            print(f"  Weights: {weights}")
            print(f"  Weights sum: {np.sum(weights):.6f}")
            
            if success:
                abs_weights = np.abs(weights)
                min_weight = np.min(abs_weights)
                portfolio_return = np.dot(mean_returns, weights)
                print(f"  Min abs weight: {min_weight:.6f}")
                print(f"  Portfolio return: {portfolio_return:.6f}")
                print(f"  Passes 0.05 threshold: {np.all(abs_weights >= 0.05)}")
            
        except Exception as e:
            print(f"  ❌ ERROR: {e}")
            import traceback
            traceback.print_exc()
        
        print()

def test_maxmodsharpe_cost_function():
    """Test the MaxModSharpe cost function calculation specifically"""
    print("🔍 Testing MaxModSharpe Cost Function")
    print("=" * 40)
    
    # Create test data
    np.random.seed(123)
    mean_returns = np.array([0.001, 0.0005, 0.002])
    returns_matrix = np.random.normal(0.001, 0.015, (3, 100))  # 3 assets x 100 periods
    
    # Test with equal weights
    test_weights = np.array([1/3, 1/3, 1/3])
    
    print(f"Test weights: {test_weights}")
    print(f"Mean returns: {mean_returns}")
    print(f"Returns matrix shape: {returns_matrix.shape}")
    print()
    
    # Calculate what the cost function should return manually
    portfolio_return = np.dot(mean_returns, test_weights)
    portfolio_series = np.dot(returns_matrix.T, test_weights)  # Time series of portfolio returns
    
    print(f"Portfolio expected return: {portfolio_return:.6f}")
    print(f"Portfolio series length: {len(portfolio_series)}")
    print(f"Portfolio series mean: {np.mean(portfolio_series):.6f}")
    print(f"Portfolio series std: {np.std(portfolio_series, ddof=1):.6f}")
    
    # Calculate skewness and kurtosis manually
    from scipy import stats
    skewness = stats.skew(portfolio_series)
    kurtosis = stats.kurtosis(portfolio_series, fisher=True)  # Excess kurtosis
    
    print(f"Skewness: {skewness:.6f}")
    print(f"Kurtosis (excess): {kurtosis:.6f}")
    
    # Calculate Cornish-Fisher adjustment
    z = 1.6448536269514722  # 95% confidence
    z_mod = z + (1.0/6.0) * (z*z - 1.0) * skewness + (1.0/24.0) * (z**3 - 3.0*z) * kurtosis - (1.0/36.0) * (2.0*z**3 - 5.0*z) * (skewness**2)
    
    portfolio_volatility = np.std(portfolio_series, ddof=1)
    mod_vol = portfolio_volatility * (z_mod / z)
    
    expected_modified_sharpe = portfolio_return / mod_vol
    expected_objective = -expected_modified_sharpe  # Negative for minimization
    
    print(f"Z: {z:.6f}")
    print(f"Z_mod: {z_mod:.6f}")
    print(f"Portfolio volatility: {portfolio_volatility:.6f}")
    print(f"Modified volatility: {mod_vol:.6f}")
    print(f"Expected Modified Sharpe: {expected_modified_sharpe:.6f}")
    print(f"Expected objective (negative): {expected_objective:.6f}")
    print()
    
    # Now test the Rust implementation
    print("Testing Rust optimization with these weights as starting point...")
    try:
        cov_matrix = np.cov(returns_matrix)
        bounds_lower = np.array([-1.0, -1.0, -1.0])
        bounds_upper = np.array([1.0, 1.0, 1.0])
        
        weights, success, objective_value = ratio_calcs_rust.optimize_portfolio_rust(
            mean_returns.astype(np.float64),
            cov_matrix.astype(np.float64),
            returns_matrix.astype(np.float64),
            'max_modified_sharpe',
            bounds_lower,
            bounds_upper,
            200
        )
        
        print(f"Rust result:")
        print(f"  Success: {success}")
        print(f"  Objective: {objective_value}")
        print(f"  Weights: {weights}")
        
        if success:
            # Calculate what we got
            rust_portfolio_return = np.dot(mean_returns, weights)
            rust_portfolio_series = np.dot(returns_matrix.T, weights)
            rust_skewness = stats.skew(rust_portfolio_series)
            rust_kurtosis = stats.kurtosis(rust_portfolio_series, fisher=True)
            rust_volatility = np.std(rust_portfolio_series, ddof=1)
            rust_z_mod = z + (1.0/6.0) * (z*z - 1.0) * rust_skewness + (1.0/24.0) * (z**3 - 3.0*z) * rust_kurtosis - (1.0/36.0) * (2.0*z**3 - 5.0*z) * (rust_skewness**2)
            rust_mod_vol = rust_volatility * (rust_z_mod / z)
            rust_modified_sharpe = rust_portfolio_return / rust_mod_vol
            
            print(f"  Portfolio return: {rust_portfolio_return:.6f}")
            print(f"  Modified Sharpe: {rust_modified_sharpe:.6f}")
            print(f"  Expected objective: {-rust_modified_sharpe:.6f}")
            print(f"  Actual objective: {objective_value:.6f}")
            print(f"  Match: {abs(objective_value - (-rust_modified_sharpe)) < 1e-6}")
        
    except Exception as e:
        print(f"❌ Rust error: {e}")
        import traceback
        traceback.print_exc()

def test_data_format_issues():
    """Test if there are data format issues causing problems"""
    print("🔍 Testing Data Format Issues")
    print("=" * 35)
    
    # Test with very simple, controlled data
    mean_returns = np.array([0.01, 0.005, 0.015])
    
    # Create returns matrix with known properties
    periods = 50
    returns_matrix = np.array([
        np.random.normal(0.01, 0.02, periods),   # Asset 1
        np.random.normal(0.005, 0.015, periods), # Asset 2  
        np.random.normal(0.015, 0.025, periods)  # Asset 3
    ])
    
    cov_matrix = np.cov(returns_matrix)
    
    print(f"Mean returns: {mean_returns}")
    print(f"Returns matrix shape: {returns_matrix.shape}")
    print(f"Cov matrix shape: {cov_matrix.shape}")
    print()
    
    # Test if any values are invalid
    print("Data validation:")
    print(f"  Mean returns finite: {np.all(np.isfinite(mean_returns))}")
    print(f"  Returns matrix finite: {np.all(np.isfinite(returns_matrix))}")
    print(f"  Cov matrix finite: {np.all(np.isfinite(cov_matrix))}")
    print(f"  Cov matrix positive definite: {np.all(np.linalg.eigvals(cov_matrix) > 0)}")
    print()
    
    # Test optimization
    bounds_lower = np.array([-1.0, -1.0, -1.0])
    bounds_upper = np.array([1.0, 1.0, 1.0])
    
    try:
        weights, success, objective_value = ratio_calcs_rust.optimize_portfolio_rust(
            mean_returns.astype(np.float64),
            cov_matrix.astype(np.float64),
            returns_matrix.astype(np.float64),
            'max_modified_sharpe',
            bounds_lower,
            bounds_upper,
            100
        )
        
        print(f"Simple data test:")
        print(f"  Success: {success}")
        print(f"  Objective: {objective_value}")
        print(f"  Weights: {weights}")
        
        if not success:
            print("❌ Even simple data fails - there's a bug in the Rust implementation")
        
    except Exception as e:
        print(f"❌ Error with simple data: {e}")

if __name__ == "__main__":
    test_rust_maxmodsharpe_directly()
    test_maxmodsharpe_cost_function()
    test_data_format_issues()
