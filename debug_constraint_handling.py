#!/usr/bin/env python3
"""
Debug constraint handling in Rust optimization
"""

import numpy as np
import pandas as pd
import ratio_calcs_rust

def test_constraint_handling():
    """Test if Rust optimization properly handles sum-to-1 constraint"""
    print("🔍 Testing Constraint Handling")
    print("=" * 35)
    
    # Create simple test data
    np.random.seed(42)
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    periods = 100
    
    returns_data = {}
    for symbol in symbols:
        returns_data[symbol] = np.random.normal(0.001, 0.015, periods)
    
    returns_df = pd.DataFrame(returns_data)
    mean_returns = returns_df.mean().values
    cov_matrix = returns_df.cov().values
    returns_matrix = returns_df.values.T
    
    print(f"Test data: {periods} periods, {len(symbols)} assets")
    print(f"Mean returns: {mean_returns}")
    print()
    
    # Test all optimization types to see if constraint handling is consistent
    optimization_types = [
        'min_variance',
        'max_sharpe', 
        'max_sortino',
        'max_omega',
        'max_calmar',
        'max_modified_sharpe'
    ]
    
    bounds_lower = np.array([-1.0, -1.0, -1.0])
    bounds_upper = np.array([1.0, 1.0, 1.0])
    
    for opt_type in optimization_types:
        print(f"--- Testing {opt_type} ---")
        try:
            weights, success, objective_value = ratio_calcs_rust.optimize_portfolio_rust(
                mean_returns.astype(np.float64),
                cov_matrix.astype(np.float64),
                returns_matrix.astype(np.float64),
                opt_type,
                bounds_lower,
                bounds_upper,
                200
            )
            
            weights_sum = np.sum(weights)
            constraint_violation = abs(weights_sum - 1.0)
            
            print(f"  Success: {success}")
            print(f"  Weights sum: {weights_sum:.10f}")
            print(f"  Constraint violation: {constraint_violation:.2e}")
            print(f"  Objective: {objective_value:.10f}")
            print(f"  Weights: {[f'{w:.6f}' for w in weights]}")
            
            if constraint_violation > 1e-6:
                print(f"  ❌ CONSTRAINT VIOLATION!")
            else:
                print(f"  ✅ Constraint satisfied")
                
        except Exception as e:
            print(f"  ❌ ERROR: {e}")
        
        print()

def test_maxmodsharpe_specific_issue():
    """Test MaxModSharpe with specific problematic data"""
    print("🔍 Testing MaxModSharpe Specific Issue")
    print("=" * 40)
    
    # Use the exact data that was causing issues
    np.random.seed(300)
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    periods = 300
    
    returns_data = {}
    returns_data['EURUSD'] = np.random.normal(0.002, 0.015, periods)   # Positive
    returns_data['GBPUSD'] = np.random.normal(-0.001, 0.018, periods)  # Negative
    returns_data['USDJPY'] = np.random.normal(0.0005, 0.012, periods)  # Small positive
    
    returns_df = pd.DataFrame(returns_data)
    mean_returns = returns_df.mean().values
    cov_matrix = returns_df.cov().values
    returns_matrix = returns_df.values.T
    
    print(f"Mean returns: {mean_returns}")
    print(f"Has negative mean return: {np.any(mean_returns < 0)}")
    print()
    
    # Test MaxModSharpe multiple times with different starting points
    bounds_lower = np.array([-1.0, -1.0, -1.0])
    bounds_upper = np.array([1.0, 1.0, 1.0])
    
    starting_points = [
        np.array([1/3, 1/3, 1/3]),
        np.array([0.5, 0.3, 0.2]),
        np.array([0.8, 0.1, 0.1]),
        np.array([0.1, 0.8, 0.1]),
        np.array([0.1, 0.1, 0.8]),
    ]
    
    for i, start_weights in enumerate(starting_points):
        print(f"--- Test {i+1}: Starting weights {start_weights} ---")
        
        try:
            # Modify the Rust code to accept custom starting weights (for now, just use default)
            weights, success, objective_value = ratio_calcs_rust.optimize_portfolio_rust(
                mean_returns.astype(np.float64),
                cov_matrix.astype(np.float64),
                returns_matrix.astype(np.float64),
                'max_modified_sharpe',
                bounds_lower,
                bounds_upper,
                200
            )
            
            print(f"  Success: {success}")
            print(f"  Objective: {objective_value:.10f}")
            print(f"  Weights: {weights}")
            print(f"  Weights sum: {np.sum(weights):.10f}")
            
            if success:
                # Check weight threshold
                abs_weights = np.abs(weights)
                min_weight = np.min(abs_weights)
                passes_threshold = np.all(abs_weights >= 0.05)
                
                print(f"  Min absolute weight: {min_weight:.6f}")
                print(f"  Passes 0.05 threshold: {passes_threshold}")
                
                # Calculate portfolio return
                portfolio_return = np.dot(mean_returns, weights)
                print(f"  Portfolio return: {portfolio_return:.6f}")
                print(f"  Return >= 0: {portfolio_return >= 0}")
                
                if not passes_threshold:
                    print(f"  ❌ Would be filtered by weight threshold")
                elif portfolio_return < 0:
                    print(f"  ❌ Would be filtered by negative return")
                else:
                    print(f"  ✅ Would pass all filters")
            
        except Exception as e:
            print(f"  ❌ ERROR: {e}")
            import traceback
            traceback.print_exc()
        
        print()

def test_numerical_precision():
    """Test if there are numerical precision issues"""
    print("🔍 Testing Numerical Precision")
    print("=" * 35)
    
    # Create data with known properties
    np.random.seed(123)
    mean_returns = np.array([0.001, 0.002, 0.0015])
    
    # Create returns matrix with controlled properties
    periods = 200
    returns_matrix = np.array([
        np.random.normal(0.001, 0.01, periods),
        np.random.normal(0.002, 0.015, periods),
        np.random.normal(0.0015, 0.012, periods)
    ])
    
    cov_matrix = np.cov(returns_matrix)
    
    print(f"Mean returns: {mean_returns}")
    print(f"Returns matrix shape: {returns_matrix.shape}")
    print(f"Cov matrix condition number: {np.linalg.cond(cov_matrix):.2e}")
    print()
    
    # Test with different precision levels
    precisions = [np.float32, np.float64]
    
    for precision in precisions:
        print(f"--- Testing with {precision.__name__} precision ---")
        
        try:
            weights, success, objective_value = ratio_calcs_rust.optimize_portfolio_rust(
                mean_returns.astype(precision),
                cov_matrix.astype(precision),
                returns_matrix.astype(precision),
                'max_modified_sharpe',
                np.array([-1.0, -1.0, -1.0], dtype=precision),
                np.array([1.0, 1.0, 1.0], dtype=precision),
                200
            )
            
            print(f"  Success: {success}")
            print(f"  Objective: {objective_value:.15f}")
            print(f"  Weights: {[f'{w:.15f}' for w in weights]}")
            print(f"  Weights sum: {np.sum(weights):.15f}")
            
        except Exception as e:
            print(f"  ❌ ERROR: {e}")
        
        print()

if __name__ == "__main__":
    test_constraint_handling()
    test_maxmodsharpe_specific_issue()
    test_numerical_precision()
