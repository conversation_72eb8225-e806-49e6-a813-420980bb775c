import numpy as np
import pandas as pd
import psutil
import multiprocessing
import time
import logging
import functools
import scipy.optimize as sco

from ratio_calcs_rust_wrapper import compute_omega_ratio, compute_calmar_ratio, neg_modified_sharpe_ratio, portfolio_variance, calculate_var_cvar_numba, neg_sharpe_ratio, neg_sortino_ratio
from func_mt5 import convert_log_to_arithmetic_returns

# Note: neg_calmar_ratio is imported locally in process_combo to ensure availability in worker processes

# Import Rust optimization engine
try:
    from portfolio_optimizer_rust import process_combo_rust, rust_optimizer, RUST_OPTIMIZATION_AVAILABLE
    print("✅ Rust portfolio optimization engine integrated successfully")
except ImportError as e:
    print(f"⚠️  Rust optimization engine not available: {e}")
    RUST_OPTIMIZATION_AVAILABLE = False

def cached_process_combo(combo_data, frontier_option=None):
    """
    Cached process combo function with Rust optimization integration
    """
    # Create a slimmed-down version of the data to reduce caching overhead
    optimized_data = []
    for combo, current_df, historical_df in combo_data:
        try:
            # Only extract the columns we need and make deep copies to avoid export conflicts
            compact_current_df = current_df[list(combo)].copy(deep=True)
            compact_historical_df = historical_df[list(combo)].copy(deep=True)

            # Ensure DataFrames are not sharing memory with parent
            compact_current_df = compact_current_df.reset_index(drop=True)
            compact_historical_df = compact_historical_df.reset_index(drop=True)

            optimized_data.append((combo, compact_current_df, compact_historical_df))
        except Exception as e:
            print(f"Error processing combo {combo}: {e}")
            continue

    # Use Rust optimization engine if available, otherwise fall back to original
    if RUST_OPTIMIZATION_AVAILABLE:
        try:
            return process_combo_rust(optimized_data)
        except Exception as e:
            print(f"⚠️  Rust optimization failed, falling back to Python: {e}")
            return process_combo_original(optimized_data)
    else:
        return process_combo_original(optimized_data)

def normalize_weights(weights):
    """Normalize weights so sum of absolute values equals 1.0"""
    import numpy as np
    # Always convert to NumPy array for consistent handling
    weights_array = np.asarray(weights)
    abs_sum = np.sum(np.abs(weights_array))
    
    # Return the original array if sum is zero
    if abs_sum <= 0:
        return weights_array
        
    # Simply normalize by dividing by sum of absolute values
    return np.round(weights_array / abs_sum, 2)

##################################################
#---OPTIMIZATION: PROCESSING COMBINATIONS---#
# This is the original process_combo function (renamed for fallback)
def process_combo_original(combo_batch):
    """Process a batch of combinations and return portfolio candidates"""
    current_usage = psutil.cpu_percent(interval=0.05)
    if current_usage > 80:
        time.sleep(0.05)
        
    batch_results = []
    
    # Pre-filter combinations to avoid expensive optimizations on poor candidates
    for combo_data in combo_batch:
        combo, current_df, historical_df = combo_data

        # Quick filtering based on mean return and Sharpe ratio to avoid expensive calculations for poor portfolios
        subset_returns = current_df[list(combo)]
        quick_mean = subset_returns.mean().mean()
        quick_std = subset_returns.mean().std()
        quick_sharpe = quick_mean / quick_std if quick_std > 0 else -999

        # Filter out portfolios with negative returns, low Sharpe ratios, and invalid combinations
        if not set(combo).issubset(current_df.columns): #or quick_mean < 0 or quick_sharpe < 0.05:
            continue

        # ----- New: Remove inversion loop; use the original combo directly -----
        adjusted = current_df[list(combo)]  # Keep log returns for chaining and calculations

        # Convert log returns to arithmetic returns for portfolio optimization (μ and Σ)
        arithmetic_returns = convert_log_to_arithmetic_returns(adjusted)
        cov_matrix = arithmetic_returns.cov()
        mean_returns = arithmetic_returns.mean()

        historical_subset = historical_df[list(combo)]
        display_combo = list(combo)  # No inversion-based sign change

        # Setup optimization parameters with bounds allowing long and short positions.
        n_assets = len(combo)
        init_guess = np.repeat(1/n_assets, n_assets)
        constraints = {'type': 'eq', 'fun': lambda x: np.sum(x) - 1}
        bounds = tuple((-1, 1) for _ in range(n_assets))

        # --- Min Variance Optimization ---
        try:
            if np.any(np.isnan(cov_matrix)) or np.any(np.isinf(cov_matrix)):
                pass
            else:
                res_mv = sco.minimize(portfolio_variance, init_guess, args=(cov_matrix,),
                                        method='SLSQP', bounds=bounds, constraints=constraints,
                                        options={'ftol': 1e-8, 'maxiter': 200})
                if res_mv.success:
                    w_mv = normalize_weights(res_mv.x)
                    if np.all(np.abs(w_mv) >= 0.05):
                        metrics = calculate_portfolio_metrics(w_mv, mean_returns, cov_matrix, adjusted)
                        # Filter out portfolios with negative returns
                        if metrics.get('return', 0) >= 0:
                            if len(historical_subset) > 0:
                                historical_portfolio = historical_subset.dot(w_mv)
                                historical_metrics = calculate_historical_metrics(historical_portfolio)
                                metrics.update(historical_metrics)
                            candidate = {
                                'combo': display_combo,
                                'weights': w_mv.tolist(),
                                'optimization': 'Min Variance',
                                **metrics
                            }
                            batch_results.append(('minvar', candidate))
                            batch_results.append(('composite', candidate.copy()))
        except Exception as e:
            print(f"Error obtaining batch results for minvar/composite: {e}")
            pass
        
        # --- Max Sharpe Optimization ---
        try:
            if np.any(np.isnan(cov_matrix)) or np.any(np.isinf(cov_matrix)):
                pass
            else:
                res_ms = sco.minimize(neg_sharpe_ratio, init_guess, args=(mean_returns, cov_matrix),
                                    method='SLSQP', bounds=bounds, constraints=constraints,
                                    options={'ftol': 1e-8, 'maxiter': 200})
                if res_ms.success:
                    w_ms = normalize_weights(res_ms.x)
                    if np.all(np.abs(w_ms) >= 0.05):
                        metrics = calculate_portfolio_metrics(w_ms, mean_returns, cov_matrix, adjusted)
                        # Filter out portfolios with negative returns
                        if metrics.get('return', 0) >= 0:
                            if len(historical_subset) > 0:
                                historical_portfolio = historical_subset.dot(w_ms)
                                historical_metrics = calculate_historical_metrics(historical_portfolio)
                                metrics.update(historical_metrics)
                            candidate = {
                                'combo': display_combo,
                                'weights': w_ms.tolist(),
                                'optimization': 'Max Sharpe',
                                **metrics
                            }
                            batch_results.append(('maxsharpe', candidate))
                        batch_results.append(('composite', candidate.copy()))
                        batch_results.append(('ss_composite', candidate.copy()))
        except Exception as e:
            print(f"Error obtaining batch results for maxsharpe/composite/ss_composite: {e}")
            pass
        
        # --- Max Sortino Optimization ---
        try:
            if np.any(np.isnan(cov_matrix)) or np.any(np.isinf(cov_matrix)):
                pass
            else:
                res_mso = sco.minimize(neg_sortino_ratio, init_guess, args=(mean_returns, cov_matrix, adjusted),
                                    method='SLSQP', bounds=bounds, constraints=constraints,
                                    options={'ftol': 1e-8, 'maxiter': 200})
                if res_mso.success:
                    w_mso = normalize_weights(res_mso.x)
                    if np.all(np.abs(w_mso) >= 0.05):
                        metrics = calculate_portfolio_metrics(w_mso, mean_returns, cov_matrix, adjusted)
                        # Filter out portfolios with negative returns
                        if metrics.get('return', 0) >= 0:
                            if len(historical_subset) > 0:
                                historical_portfolio = historical_subset.dot(w_mso)
                                historical_metrics = calculate_historical_metrics(historical_portfolio)
                                metrics.update(historical_metrics)
                            candidate = {
                                'combo': display_combo,
                                'weights': w_mso.tolist(),
                                'optimization': 'Max Sortino',
                                **metrics
                            }
                            batch_results.append(('maxsortino', candidate))
                            batch_results.append(('ss_composite', candidate.copy()))
        except Exception as e:
            print(f"Error obtaining batch results for maxsortino/ss_composite: {e}")
            pass
        
        # --- Max Omega Optimization ---
        try:
            if np.any(np.isnan(cov_matrix)) or np.any(np.isinf(cov_matrix)):
                pass
            else:
                res_omega = sco.minimize(lambda w: -compute_omega_ratio(adjusted.dot(w), 0.0), 
                                        init_guess,
                                        method='SLSQP', bounds=bounds, constraints=constraints,
                                        options={'ftol': 1e-8, 'maxiter': 200})
                if res_omega.success:
                    w_omega = normalize_weights(res_omega.x)
                    if np.all(np.abs(res_omega.x) >= 0.05):
                        metrics = calculate_portfolio_metrics(w_omega, mean_returns, cov_matrix, adjusted)
                        # Filter out portfolios with negative returns
                        if metrics.get('return', 0) >= 0:
                            if len(historical_subset) > 0:
                                historical_portfolio = historical_subset.dot(w_omega)
                                historical_metrics = calculate_historical_metrics(historical_portfolio)
                                metrics.update(historical_metrics)
                            candidate = {
                                'combo': display_combo,
                                'weights': w_omega.tolist(),
                                'optimization': 'Max Omega',
                                **metrics
                            }
                            batch_results.append(('maxomega', candidate))
        except Exception as e:
            print(f"Error obtaining batch results for maxomega: {e}")
            pass
        
        # --- Max Calmar Optimization ---
        try:
            if np.any(np.isnan(cov_matrix)) or np.any(np.isinf(cov_matrix)):
                pass
            else:
                # Define neg_calmar_ratio locally to ensure it's available in worker processes
                def neg_calmar_ratio_local(weights, mean_returns, cov_matrix, adjusted):
                    """Local implementation of neg_calmar_ratio for multiprocessing compatibility"""
                    try:
                        from ratio_calcs_rust_wrapper import compute_calmar_ratio
                        calmar = compute_calmar_ratio(weights, mean_returns, cov_matrix, adjusted)
                        return -calmar if calmar is not None and not np.isnan(calmar) and not np.isinf(calmar) else 1e6
                    except Exception as e:
                        print(f"Error in neg_calmar_ratio_local: {e}")
                        return 1e6

                res_calmar = sco.minimize(neg_calmar_ratio_local, init_guess, args=(mean_returns, cov_matrix, adjusted),
                                        method='SLSQP', bounds=bounds, constraints=constraints,
                                        options={'ftol': 1e-8, 'maxiter': 200})
                if res_calmar.success:
                    w_calmar = normalize_weights(res_calmar.x)
                    if np.all(np.abs(res_calmar.x) >= 0.05):
                        metrics = calculate_portfolio_metrics(w_calmar, mean_returns, cov_matrix, adjusted)
                        # Filter out portfolios with negative returns
                        if metrics.get('return', 0) >= 0:
                            if len(historical_subset) > 0:
                                historical_portfolio = historical_subset.dot(w_calmar)
                                historical_metrics = calculate_historical_metrics(historical_portfolio)
                                metrics.update(historical_metrics)
                            candidate = {
                                'combo': display_combo,
                                'weights': w_calmar.tolist(),
                                'optimization': 'Max Calmar',
                                **metrics
                            }
                            batch_results.append(('maxcalmar', candidate))
        except Exception as e:
            print(f"Error obtaining batch results for maxcalmar: {e}")
            pass

        # --- Max Modified Sharpe Optimization ---
        try:
            if np.any(np.isnan(cov_matrix)) or np.any(np.isinf(cov_matrix)):
                pass
            else:
                res_mod = sco.minimize(neg_modified_sharpe_ratio, init_guess, 
                                    args=(mean_returns, cov_matrix, adjusted, 0.0),
                                    method='SLSQP', bounds=bounds, constraints=constraints,
                                    options={'ftol': 1e-8, 'maxiter': 200})
                if res_mod.success:
                    w_mod = normalize_weights(res_mod.x)
                    if np.all(np.abs(res_mod.x) >= 0.05):
                        metrics_mod = calculate_portfolio_metrics(w_mod, mean_returns, cov_matrix, adjusted)
                        # Filter out portfolios with negative returns
                        if metrics_mod.get('return', 0) >= 0:
                            if len(historical_subset) > 0:
                                historical_portfolio = historical_subset.dot(w_mod)
                                historical_metrics = calculate_historical_metrics(historical_portfolio)
                                metrics_mod.update(historical_metrics)
                            candidate_mod = {
                                'combo': display_combo,
                                'weights': w_mod.tolist(),
                                'optimization': 'Max CF Sharpe',
                                **metrics_mod
                            }
                            batch_results.append(('maxmodsharpe', candidate_mod))
        except Exception as e:
            print(f"Error obtaining batch results for maxmodsharpe: {e}")
            pass

    return batch_results

def get_optimal_cores(max_usage_percent=80):
    # Input validation
    if not isinstance(max_usage_percent, (int, float)):
        raise ValueError("max_usage_percent must be a number")
    if not 1 <= max_usage_percent <= 100:
        raise ValueError("max_usage_percent must be between 1 and 100")
    
    # Get system information
    available_cores = multiprocessing.cpu_count()
    
    # Get current CPU usage with a short interval
    try:
        current_usage = psutil.cpu_percent(interval=0.1)
    except Exception as e:
        # Fall back to a conservative number of cores if we can't get CPU usage
        print(f"Warning: Could not get CPU usage: {e}. Using 2 cores.")
        return min(2, available_cores - 1)
    
    # If already at high usage, be conservative
    if current_usage >= max_usage_percent:
        return min(2, available_cores)
    
    # Calculate how many cores we can use based on available headroom
    available_headroom = max_usage_percent - current_usage
    cores_to_use = int(available_headroom / (100.0/available_cores))
    
    # Ensure we use at least 1 core and leave some cores for the system
    return max(1, min(available_cores - 2, cores_to_use))

# Configure logging (should be done once in the main module)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def timing_decorator(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Record start time
        start = time.perf_counter()
        
        # Execute the function
        try:
            result = func(*args, **kwargs)
        except Exception as e:
            # Log the error with timing information
            elapsed = time.perf_counter() - start
            logger.error(f"Function {func.__name__} failed after {elapsed:.4f} seconds: {e}")
            raise  # Re-raise the exception
            
        # Calculate elapsed time
        elapsed = time.perf_counter() - start
        
        # Log the timing information
        logger.info(f'Function {func.__name__} took {elapsed:.4f} seconds')
        
        return result
    return wrapper

def calculate_portfolio_metrics(weights, mean_returns, cov_matrix, adjusted):
    """
    Calculate portfolio metrics.

    Args:
        weights: Portfolio weights
        mean_returns: Mean returns vector
        cov_matrix: Covariance matrix
        adjusted: Adjusted returns data
    """
    w = np.array(weights)
    port_return = np.dot(w, mean_returns)
    try:
        port_risk = np.sqrt(portfolio_variance(w, cov_matrix))
        if np.isnan(port_risk) or np.isinf(port_risk):
            port_risk = 1e-10
    except Exception as e:
        print(f"Error in risk calculation: {e}")
        port_risk = 1e-10
    try:
        if isinstance(adjusted, pd.DataFrame):
            candidate_series = adjusted.dot(w)
        else:
            candidate_series = adjusted @ w
        if not isinstance(candidate_series, pd.Series):
            candidate_series_df = pd.Series(candidate_series, index=adjusted.index)
        else:
            candidate_series_df = candidate_series
    except Exception as e:
        print(f"Error in return series calculation: {e}")
        candidate_series = np.zeros(len(adjusted))
        candidate_series_df = pd.Series(candidate_series, index=adjusted.index)
    try:
        if isinstance(candidate_series_df, pd.Series):
            candidate_values = candidate_series_df.values
        else:
            candidate_values = np.array(candidate_series_df)
        downside_mask = candidate_values < 0
        if np.any(downside_mask):
            downside_values = candidate_values[downside_mask]
            if len(downside_values) > 1:
                drisk = np.std(downside_values)
                if np.isnan(drisk) or np.isinf(drisk) or drisk <= 0:
                    drisk = 1e-10
            else:
                drisk = 1e-10
            sortino = port_return / drisk if drisk > 0 else 0
            if np.isnan(sortino) or np.isinf(sortino):
                sortino = 0
        else:
            drisk = 1e-10
            sortino = 0 if port_return <= 0 else 100
    except Exception as e:
        print(f"Error in downside metrics calculation: {e}")
        drisk = 1e-10
        sortino = 0
    try:
        sharpe = port_return / port_risk if port_risk > 0 else 0
        if np.isnan(sharpe) or np.isinf(sharpe):
            sharpe = 0
    except Exception as e:
        print(f"Error in Sharpe calculation: {e}")
        sharpe = 0
    try:
        omega = compute_omega_ratio(candidate_series_df, threshold=0.0)
        # Allow Inf to pass through, handle formatting in display layer if needed
        if np.isnan(omega):
             omega = 0 # Assign 0 only if NaN
    except Exception as e:
        print(f"Error in Omega calculation: {e}")
        omega = 0
    try:
        calmar = compute_calmar_ratio(candidate_series_df)
        # Allow Inf to pass through, handle formatting in display layer if needed
        if np.isnan(calmar):
             calmar = 0 # Assign 0 only if NaN
    except Exception as e:
        print(f"Error in Calmar calculation: {e}")
        calmar = 0
    try:
        mod_sharpe = -neg_modified_sharpe_ratio(w, mean_returns, cov_matrix, adjusted, 0.0)
        if np.isnan(mod_sharpe) or np.isinf(mod_sharpe):
            mod_sharpe = 0
    except Exception as e:
        print(f"Error in Modified Sharpe calculation: {e}")
        mod_sharpe = 0

    metrics = {
        'return': float(port_return),
        'risk': float(port_risk),
        'drisk': float(drisk),
        'sharpe': float(sharpe),
        'sortino': float(sortino),
        'omega': float(omega),
        'calmar': float(calmar),
        'mod_sharpe': float(mod_sharpe),
    }

    return metrics

def calculate_historical_metrics(historical_portfolio):
    try:
        cum_returns = historical_portfolio.cumsum()
        rolling_max = cum_returns.expanding().max()
        drawdowns = (cum_returns - rolling_max) / rolling_max
        squared_drawdowns = drawdowns ** 2
        ulcer_index = np.sqrt(squared_drawdowns.mean())
        annualized_return = cum_returns.iloc[-1] / len(cum_returns) * 252
        martin_ratio = annualized_return / (ulcer_index + 1e-10)
        pain_index = abs(drawdowns).mean()
        annualized_return = cum_returns.iloc[-1] / len(cum_returns) * 252
        pain_ratio = annualized_return / (pain_index + 1e-10)
        is_drawdown = drawdowns < 0
        if is_drawdown.any():
            is_drawdown_array = is_drawdown.values
            runs = np.split(is_drawdown_array, np.where(np.diff(is_drawdown_array.astype(int)) != 0)[0] + 1)
            drawdown_durations = [len(run) for run in runs if len(run) > 0 and run[0]]
            max_drawdown_duration = max(drawdown_durations) if drawdown_durations else 0
        else:
            max_drawdown_duration = 0
        clean_values = historical_portfolio.values[~np.isnan(historical_portfolio.values)]
        if len(clean_values) > 0:
            var_95, cvar_95 = calculate_var_cvar_numba(clean_values, 0.95)
        else:
            var_95, cvar_95 = 0.0, 0.0
        return {
            'ulcer_index': float(ulcer_index),
            'pain_index': float(pain_index),
            'pain_ratio': float(pain_ratio),
            'max_drawdown_duration': int(max_drawdown_duration),
            'var_95': float(var_95),
            'cvar_95': float(cvar_95),
            'martin': float(martin_ratio)
        }
    except Exception as e:
        print(f"Error calculating historical metrics: {e}")
        return {
            'ulcer_index': 0.0,
            'pain_index': 0.0,
            'pain_ratio': 0.0,
            'max_drawdown_duration': 0,
            'var_95': 0.0,
            'cvar_95': 0.0,
            'martin': 0.0
        }
    


def compute_rsi(series, length=14):

    if not isinstance(series, pd.Series):
        raise ValueError("Input must be a pandas Series")
    if not isinstance(length, int) or length < 2:
        raise ValueError("Length must be an integer >= 2")
    if len(series) <= length:
        return pd.Series(np.nan, index=series.index)
    
    # Calculate price changes
    delta = series.diff()
    
    gain = delta.copy()
    loss = delta.copy()
    
    gain[gain < 0] = 0
    loss[loss > 0] = 0
    loss = -loss  # Convert losses to positive values
    
    avg_gain = pd.Series(index=series.index)
    avg_loss = pd.Series(index=series.index)
    
    avg_gain.iloc[length] = gain.iloc[1:length+1].mean()
    avg_loss.iloc[length] = loss.iloc[1:length+1].mean()
    
    for i in range(length+1, len(series)):
        avg_gain.iloc[i] = (avg_gain.iloc[i-1] * (length-1) + gain.iloc[i]) / length
        avg_loss.iloc[i] = (avg_loss.iloc[i-1] * (length-1) + loss.iloc[i]) / length
    
    rs = avg_gain / avg_loss.replace(0, 1e-10)
    rsi = 100.0 - (100.0 / (1.0 + rs))

    return rsi

##################################################
#---RUST OPTIMIZATION INTEGRATION---#
def process_combo(combo_batch):
    """
    Main process_combo function with Rust optimization integration

    This function serves as the primary entry point for portfolio optimization.
    It automatically uses the high-performance Rust engine when available,
    and falls back to the original Python implementation if needed.

    Args:
        combo_batch: List of (combo, current_df, historical_df) tuples

    Returns:
        List of optimized portfolio candidates
    """
    if RUST_OPTIMIZATION_AVAILABLE:
        try:
            # Use Rust optimization engine for significant performance improvement
            start_time = time.time()
            results = process_combo_rust(combo_batch)
            rust_time = time.time() - start_time

            # Log performance statistics
            stats = rust_optimizer.optimization_stats
            print(f"🚀 Rust optimization completed in {rust_time:.3f}s")
            print(f"   Total optimizations: {stats['total_optimizations']}")
            print(f"   Rust optimizations: {stats['rust_optimizations']}")
            print(f"   Python fallbacks: {stats['python_fallbacks']}")

            return results

        except Exception as e:
            print(f"⚠️  Rust optimization failed: {e}")
            print("   Falling back to original Python implementation...")
            return process_combo_original(combo_batch)
    else:
        print("ℹ️  Using original Python optimization (Rust not available)")
        return process_combo_original(combo_batch)