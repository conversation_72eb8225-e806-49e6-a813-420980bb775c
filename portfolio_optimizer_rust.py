"""
Rust-based Portfolio Optimization Engine
High-performance replacement for scipy.optimize-based portfolio optimization
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import time

# Try to import the Rust module, fall back to original if not available
try:
    import ratio_calcs_rust
    RUST_OPTIMIZATION_AVAILABLE = True
    print("Rust portfolio optimization engine loaded successfully")
except ImportError:
    RUST_OPTIMIZATION_AVAILABLE = False
    print("Rust portfolio optimization engine not available, falling back to Python")
    # Import original functions as fallback
    from func_rest import process_combo as process_combo_python

from ratio_calcs_rust_wrapper import (
    portfolio_variance, neg_sharpe_ratio, neg_sortino_ratio,
    compute_omega_ratio, compute_calmar_ratio, neg_modified_sharpe_ratio,
    calculate_var_cvar_numba
)
from func_mt5 import convert_log_to_arithmetic_returns

class RustPortfolioOptimizer:
    """
    High-performance Rust-based portfolio optimization engine
    """
    
    def __init__(self):
        self.rust_available = RUST_OPTIMIZATION_AVAILABLE
        self.optimization_stats = {
            'total_optimizations': 0,
            'rust_optimizations': 0,
            'python_fallbacks': 0,
            'total_time_saved': 0.0
        }
    
    def optimize_single_portfolio(
        self,
        mean_returns: np.ndarray,
        cov_matrix: np.ndarray,
        returns_matrix: np.ndarray,
        optimization_type: str,
        bounds: Tuple[float, float] = (-1.0, 1.0),
        max_iterations: int = 200
    ) -> Tuple[np.ndarray, bool, float]:
        """
        Optimize a single portfolio using Rust engine
        
        Args:
            mean_returns: Expected returns for each asset
            cov_matrix: Covariance matrix of returns
            returns_matrix: Historical returns matrix for advanced calculations
            optimization_type: 'min_variance', 'max_sharpe', or 'max_sortino'
            bounds: Weight bounds for each asset
            max_iterations: Maximum optimization iterations
            
        Returns:
            Tuple of (optimal_weights, success, objective_value)
        """
        if not self.rust_available:
            raise RuntimeError("Rust optimization engine not available")
        
        n_assets = len(mean_returns)
        bounds_lower = np.full(n_assets, bounds[0])
        bounds_upper = np.full(n_assets, bounds[1])
        
        try:
            start_time = time.time()
            
            weights, success, objective_value = ratio_calcs_rust.optimize_portfolio_rust(
                mean_returns.astype(np.float64),
                cov_matrix.astype(np.float64),
                returns_matrix.astype(np.float64),
                optimization_type,
                bounds_lower,
                bounds_upper,
                max_iterations
            )
            
            optimization_time = time.time() - start_time
            self.optimization_stats['total_optimizations'] += 1
            self.optimization_stats['rust_optimizations'] += 1
            
            return np.array(weights), success, objective_value
            
        except Exception as e:
            print(f"Rust optimization failed: {e}")
            self.optimization_stats['python_fallbacks'] += 1
            raise
    
    def batch_optimize_portfolios(
        self,
        combinations_data: List[Tuple[np.ndarray, np.ndarray, np.ndarray]],
        optimization_types: List[str] = ['min_variance', 'max_sharpe', 'max_sortino'],
        bounds: Tuple[float, float] = (-1.0, 1.0),
        max_iterations: int = 200
    ) -> List[List[Tuple[np.ndarray, bool, float, str]]]:
        """
        Batch optimize multiple portfolio combinations using Rust engine
        
        Args:
            combinations_data: List of (mean_returns, cov_matrix, returns_matrix) tuples
            optimization_types: List of optimization types to run
            bounds: Weight bounds for each asset
            max_iterations: Maximum optimization iterations
            
        Returns:
            List of optimization results for each combination
        """
        if not self.rust_available:
            raise RuntimeError("Rust optimization engine not available")
        
        # Convert data to the format expected by Rust
        rust_combinations_data = []
        for mean_returns, cov_matrix, returns_matrix in combinations_data:
            rust_combinations_data.append((
                mean_returns.tolist(),
                cov_matrix.tolist(),
                returns_matrix.tolist()
            ))
        
        n_assets = len(combinations_data[0][0]) if combinations_data else 0
        bounds_lower = np.full(n_assets, bounds[0])
        bounds_upper = np.full(n_assets, bounds[1])
        
        try:
            start_time = time.time()
            
            results = ratio_calcs_rust.batch_optimize_portfolios_rust(
                rust_combinations_data,
                optimization_types,
                bounds_lower,
                bounds_upper,
                max_iterations
            )
            
            optimization_time = time.time() - start_time
            self.optimization_stats['total_optimizations'] += len(combinations_data) * len(optimization_types)
            self.optimization_stats['rust_optimizations'] += len(combinations_data) * len(optimization_types)
            
            # Convert results back to numpy arrays
            converted_results = []
            for combo_results in results:
                converted_combo = []
                for weights, success, obj_val, opt_type in combo_results:
                    converted_combo.append((
                        np.array(weights),
                        success,
                        obj_val,
                        opt_type
                    ))
                converted_results.append(converted_combo)
            
            return converted_results
            
        except Exception as e:
            print(f"Rust batch optimization failed: {e}")
            self.optimization_stats['python_fallbacks'] += len(combinations_data) * len(optimization_types)
            raise

def normalize_weights(weights):
    """Normalize weights to sum to 1"""
    abs_sum = np.sum(np.abs(weights))
    if abs_sum > 1e-15:
        return weights / abs_sum
    return weights

def calculate_portfolio_metrics(weights, mean_returns, cov_matrix, adjusted_returns):
    """Calculate comprehensive portfolio metrics"""
    portfolio_return = np.dot(weights, mean_returns)
    portfolio_variance_val = portfolio_variance(weights, cov_matrix)
    portfolio_volatility = np.sqrt(portfolio_variance_val)
    
    # Calculate portfolio returns series
    portfolio_series = adjusted_returns.dot(weights)
    
    metrics = {
        'return': portfolio_return,
        'risk': portfolio_volatility,
        'variance': portfolio_variance_val,
    }
    
    # Calculate financial ratios
    if portfolio_volatility > 1e-15:
        metrics['sharpe'] = portfolio_return / portfolio_volatility
    else:
        metrics['sharpe'] = 0.0
    
    # Sortino ratio
    try:
        downside_returns = portfolio_series[portfolio_series < 0]
        if len(downside_returns) > 0:
            downside_std = np.std(downside_returns)
            if downside_std > 1e-15:
                metrics['sortino'] = portfolio_return / downside_std
            else:
                metrics['sortino'] = 0.0
        else:
            metrics['sortino'] = 0.0
    except:
        metrics['sortino'] = 0.0
    
    # Omega ratio
    try:
        metrics['omega'] = compute_omega_ratio(portfolio_series, 0.0)
    except:
        metrics['omega'] = 0.0
    
    # Calmar ratio
    try:
        metrics['calmar'] = compute_calmar_ratio(portfolio_series)
    except:
        metrics['calmar'] = 0.0
    
    # Modified Sharpe ratio
    try:
        metrics['mod_sharpe'] = -neg_modified_sharpe_ratio(
            weights, mean_returns, cov_matrix, adjusted_returns, 0.0
        )
    except:
        metrics['mod_sharpe'] = 0.0
    
    # CVaR
    try:
        if hasattr(portfolio_series, 'values'):
            values_array = portfolio_series.values.astype(np.float64)
        else:
            values_array = np.array(portfolio_series, dtype=np.float64)
        
        clean_values = values_array[~np.isnan(values_array)]
        if len(clean_values) > 0:
            _, cvar_95 = calculate_var_cvar_numba(clean_values, 0.95)
            metrics['cvar_95'] = abs(cvar_95)
        else:
            metrics['cvar_95'] = 0.0
    except:
        metrics['cvar_95'] = 0.0
    
    return metrics

def calculate_historical_metrics(portfolio_series):
    """Calculate historical performance metrics"""
    try:
        if isinstance(portfolio_series, pd.Series):
            values = portfolio_series.values
        else:
            values = np.array(portfolio_series)
        
        # Remove NaN values
        clean_values = values[~np.isnan(values)]
        
        if len(clean_values) == 0:
            return {}
        
        # Calculate cumulative returns
        cumulative_returns = np.cumsum(clean_values)
        
        # Calculate maximum drawdown
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = running_max - cumulative_returns
        max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0.0
        
        # Calculate other historical metrics
        total_return = cumulative_returns[-1] if len(cumulative_returns) > 0 else 0.0
        volatility = np.std(clean_values) if len(clean_values) > 1 else 0.0
        
        return {
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'historical_volatility': volatility,
            'periods': len(clean_values)
        }
        
    except Exception as e:
        print(f"Error calculating historical metrics: {e}")
        return {}

# Global optimizer instance
rust_optimizer = RustPortfolioOptimizer()

def process_combo_rust(combo_batch):
    """
    Rust-optimized version of process_combo function
    Replaces the scipy.optimize-based implementation with high-performance Rust
    """
    if not RUST_OPTIMIZATION_AVAILABLE:
        print("Rust optimization not available, falling back to Python implementation")
        return process_combo_python(combo_batch)

    import psutil
    import time

    current_usage = psutil.cpu_percent(interval=0.05)
    if current_usage > 80:
        time.sleep(0.05)

    batch_results = []

    # Process all combinations without pre-filtering (as per user requirements)
    for combo_data in combo_batch:
        combo, current_df, historical_df = combo_data

        # Only check for basic data validity, no performance pre-filtering
        if not set(combo).issubset(current_df.columns):
            continue

        # Use the original combo directly (no inversion loop)
        adjusted = current_df[list(combo)]  # Keep log returns for chaining and calculations

        # Convert log returns to arithmetic returns for portfolio optimization (μ and Σ)
        arithmetic_returns = convert_log_to_arithmetic_returns(adjusted)
        cov_matrix = arithmetic_returns.cov().values
        mean_returns = arithmetic_returns.mean().values

        historical_subset = historical_df[list(combo)]
        display_combo = list(combo)  # No inversion-based sign change

        # Setup optimization parameters with bounds allowing long and short positions
        n_assets = len(combo)
        bounds = (-1.0, 1.0)  # Allow long and short positions

        # Check for invalid data
        if np.any(np.isnan(cov_matrix)) or np.any(np.isinf(cov_matrix)):
            continue
        if np.any(np.isnan(mean_returns)) or np.any(np.isinf(mean_returns)):
            continue

        # Prepare returns matrix for Rust optimization
        returns_matrix = adjusted.values.T  # Transpose to get assets x time_periods

        # Define optimization types to run
        optimization_configs = [
            ('min_variance', 'Min Variance', ['minvar', 'composite']),
            ('max_sharpe', 'Max Sharpe', ['maxsharpe', 'composite', 'ss_composite']),
            ('max_sortino', 'Max Sortino', ['maxsortino', 'ss_composite']),
            ('max_omega', 'Max Omega', ['maxomega']),
            ('max_calmar', 'Max Calmar', ['maxcalmar']),
            ('max_modified_sharpe', 'Max CF Sharpe', ['maxmodsharpe']),
        ]

        try:
            # Run optimizations using Rust engine
            for opt_type, opt_name, result_keys in optimization_configs:
                try:
                    weights, success, objective_value = rust_optimizer.optimize_single_portfolio(
                        mean_returns=mean_returns,
                        cov_matrix=cov_matrix,
                        returns_matrix=returns_matrix,
                        optimization_type=opt_type,
                        bounds=bounds,
                        max_iterations=200
                    )

                    if success:
                        # Normalize weights
                        weights = normalize_weights(weights)

                        # Check minimum weight threshold
                        if np.all(np.abs(weights) >= 0.10):
                            # Calculate comprehensive metrics
                            metrics = calculate_portfolio_metrics(weights, mean_returns, cov_matrix, adjusted)

                            # Filter out portfolios with negative returns
                            if metrics.get('return', 0) >= 0:
                                # Calculate historical metrics if available
                                if len(historical_subset) > 0:
                                    historical_portfolio = historical_subset.dot(weights)
                                    historical_metrics = calculate_historical_metrics(historical_portfolio)
                                    metrics.update(historical_metrics)

                                # Create candidate
                                candidate = {
                                    'combo': display_combo,
                                    'weights': weights.tolist(),
                                    'optimization': opt_name,
                                    **metrics
                                }

                                # Add to results with appropriate keys
                                for key in result_keys:
                                    batch_results.append((key, candidate.copy()))

                except Exception as e:
                    print(f"Error in Rust optimization {opt_type}: {e}")
                    continue

            # All optimization types are now handled by Rust implementation above
            # No more Python fallbacks needed

        except Exception as e:
            print(f"Error processing combination {combo}: {e}")
            continue

    return batch_results
