import MetaTrader5 as mt5
import numpy as np
import pandas as pd

from func_rest import calculate_portfolio_metrics
from func_mt5 import fetch_data, calculate_returns, convert_log_to_arithmetic_returns
from collections import Counter
from weekend_utils import should_use_friday_data, get_last_friday_end

# Helper function to calculate correlation between two portfolios
def _calculate_portfolio_correlation(original_portfolio_dict, candidate_portfolio_dict, returns_df):
    """Calculates the Pearson correlation between the return series of two portfolios."""
    if returns_df is None or returns_df.empty:
        # print("Warning: Returns data is missing or empty for correlation calculation.")
        return None

    def get_portfolio_series(portfolio_dict, returns_df):
        """Calculates the return series for a single portfolio."""
        try:
            # Ensure combo and weights exist and are lists
            combo = portfolio_dict.get('combo', [])
            weights = portfolio_dict.get('weights', [])
            if not isinstance(combo, list) or not isinstance(weights, list):
                 # print(f"Warning: Invalid combo or weights type for portfolio {combo}")
                 return None

            symbols = [s for s in combo if s in returns_df.columns]

            if not symbols:
                # print(f"Warning: No valid symbols found in returns data for portfolio {combo}")
                return None # Cannot calculate if no symbols match columns

            # Get weights corresponding to valid symbols ONLY
            valid_weights_list = []
            original_indices = {s: i for i, s in enumerate(combo)}
            for s in symbols:
                 idx = original_indices.get(s)
                 if idx is not None and idx < len(weights):
                     valid_weights_list.append(weights[idx])
                 else:
                     # This case should ideally not happen if combo/weights are consistent
                     # print(f"Warning: Could not find weight for symbol {s} in portfolio {combo}")
                     return None # Mismatch or missing weight

            if len(valid_weights_list) != len(symbols):
                 # print(f"Warning: Mismatch between valid symbols and weights for portfolio {combo}")
                 return None # Should have same length

            valid_weights = np.array(valid_weights_list)

            if len(valid_weights) == 0 or np.sum(np.abs(valid_weights)) == 0:
                 # print(f"Warning: Zero weights or sum of absolute weights is zero for portfolio {combo}")
                 return None # No valid weights or zero sum

            # Normalize weights within the portfolio before calculating series
            normalized_weights = valid_weights / np.sum(np.abs(valid_weights))

            portfolio_series = returns_df[symbols].dot(normalized_weights)
            return portfolio_series
        except Exception as e:
            print(f"Error calculating portfolio series for {portfolio_dict.get('combo')}: {e}")
            return None

    original_series = get_portfolio_series(original_portfolio_dict, returns_df)
    candidate_series = get_portfolio_series(candidate_portfolio_dict, returns_df)

    if original_series is None or candidate_series is None:
        # print("Warning: Could not generate one or both portfolio series.")
        return None # Cannot compute correlation if series are invalid

    # Drop NaNs that might result from dot product if original data had NaNs
    original_series = original_series.dropna()
    candidate_series = candidate_series.dropna()

    if original_series.empty or candidate_series.empty:
         # print("Warning: Portfolio series became empty after dropping NaNs.")
         return None

    try:
        # Ensure series have the same index for correlation calculation
        common_index = original_series.index.intersection(candidate_series.index)
        if len(common_index) < 5: # Need a few data points for meaningful correlation
             # print(f"Warning: Less than 5 common data points ({len(common_index)}) for correlation.")
             return None

        # Align series to common index before calculating correlation
        aligned_original = original_series.loc[common_index]
        aligned_candidate = candidate_series.loc[common_index]

        # Check for constant series (zero standard deviation)
        if aligned_original.std() < 1e-10 or aligned_candidate.std() < 1e-10:
            # print("Warning: One or both series have zero standard deviation. Correlation is undefined.")
            # Return 0 or None? Returning None seems safer.
            return None


        corr = aligned_original.corr(aligned_candidate)
        return corr if pd.notna(corr) else None # Use pd.notna for robust NaN check
    except Exception as e:
        print(f"Error computing correlation between portfolios: {e}")
        return None

def generate_portfolio_suggestions(clicked_portfolio, all_candidates, max_sharpe_risk):
    """Generate three portfolio suggestions based on clicked portfolio with specific risk constraints"""
    print(f"Starting suggestion generation with {len(all_candidates)} candidates")
    
    # Verify the clicked portfolio has the required fields
    if not isinstance(clicked_portfolio, dict) or 'combo' not in clicked_portfolio:
        print("Invalid clicked portfolio format")
        return []
        
    # Extract the clicked portfolio's pairs
    original_pairs = clicked_portfolio['combo']
    print(f"Original pairs: {original_pairs}")
    
    # Determine which risk measure to use.
    # If the clicked portfolio indicates use_cvar==True then we compare by cvar_95 (CVaR from historical metrics).
    if clicked_portfolio.get("use_cvar", False):
        risk_key = "cvar_95"
        print("Using CVaR as risk measure for suggestion filtering.")
    else:
        risk_key = "risk"

    # Fetch recent returns data to compute covariances - handle errors gracefully
    symbols = set(original_pairs)
    try:
        for candidate in all_candidates:
            if isinstance(candidate, dict) and 'combo' in candidate:
                candidate_pairs = candidate['combo']
                symbols.update(candidate_pairs)
    except Exception as e:
        print(f"Error collecting symbols: {e}")
        symbols = set(original_pairs)  # Fallback to original pairs only
    
    # Fetch data with error handling
    recent_returns_df = None
    try:
        # Check if we should use Friday data during weekend
        if should_use_friday_data():
            from datetime import datetime, timedelta
            import pytz

            # Calculate Friday's timeframe
            SERVER_TIMEZONE = pytz.timezone('Europe/Bucharest')
            now = datetime.now(SERVER_TIMEZONE)
            weekday = now.weekday()

            if weekday >= 5:  # Weekend
                friday_end = get_last_friday_end()
                friday_start = friday_end.replace(hour=0, minute=0, second=0, microsecond=0)
                print(f"Weekend mode: Fetching Friday's data from {friday_start} to {friday_end}")
                market_data = fetch_data(list(symbols), timeframe=mt5.TIMEFRAME_M15, shift=0, start_time=friday_start, end_time=friday_end)
            else:
                # Use a smaller window to compute recent covariance (last 72 hours)
                hours_to_fetch = 96
                market_data = fetch_data(list(symbols), timeframe=mt5.TIMEFRAME_M15, shift=0, hours=hours_to_fetch)
        else:
            # Use a smaller window to compute recent covariance (last 72 hours)
            hours_to_fetch = 96
            market_data = fetch_data(list(symbols), timeframe=mt5.TIMEFRAME_M15, shift=0, hours=hours_to_fetch)

        recent_returns_df = calculate_returns(market_data)
    except Exception as e:
        print(f"Error fetching data for covariance calculation: {e}")
    
    # --- Pre-calculate correlation and validity for all candidates ---
    print("Pre-calculating correlations and validity for all candidates...")
    valid_candidates_pool = []
    if all_candidates and isinstance(all_candidates, list):
        for candidate in all_candidates:
            # Basic validation
            if not isinstance(candidate, dict) or 'combo' not in candidate or 'weights' not in candidate or not isinstance(candidate['combo'], list) or not isinstance(candidate['weights'], list):
                continue

            # Check pair overlap
            candidate_pairs = candidate['combo']
            overlap = set(original_pairs).intersection(set(candidate_pairs))
            if len(overlap) > 2: # Allow up to 2 overlapping symbols
                continue

            # Check currency distribution
            combined_pairs_set = set(original_pairs)
            for p in candidate['combo']:
                 combined_pairs_set.add(p.lstrip('-'))
            all_pairs_list = list(combined_pairs_set)

            if len(all_pairs_list) < 6:
                continue

            currencies = {"EUR": 0, "USD": 0, "GBP": 0, "AUD": 0, "NZD": 0, "CAD": 0, "CHF": 0, "JPY": 0}
            valid_dist = True
            for pair in all_pairs_list:
                if len(pair) >= 6:
                    base, quote = pair[:3], pair[3:6]
                    if base in currencies: 
                        currencies[base] += 1
                    if quote in currencies: 
                        currencies[quote] += 1
                    if currencies.get(base, 0) > 2 or currencies.get(quote, 0) > 2:
                        valid_dist = False
                        break
            if not valid_dist:
                continue

            # Calculate correlation score using the helper function
            correlation_score = _calculate_portfolio_correlation(clicked_portfolio, candidate, recent_returns_df)
            candidate['correlation_score'] = correlation_score # Store it, even if None

            # Add candidate if it passes all checks
            valid_candidates_pool.append(candidate)
    else:
         print("Warning: all_candidates is not a valid list or is empty.")

    print(f"Found {len(valid_candidates_pool)} candidates passing initial filters.")

    # --- Sequential Suggestion Generation with Fallback ---
    suggestions = []
    used_combos = set() # Track used combos (as tuples of sorted strings)
    # Initialize counter for symbols across original + suggestions
    cumulative_symbol_counts = Counter(original_pairs)
    original_pairs_set = set(original_pairs) # For quick lookup

    # Define the targets and fallbacks
    suggestion_targets = [
        {'risk_range': (0.60 * max_sharpe_risk, 0.80 * max_sharpe_risk), 'risk_desc': "80% risk line", 'fallback_corr': (0.40, 0.60), 'fallback_desc': "Fallback (Mid Corr)"},
        {'risk_range': (0.40 * max_sharpe_risk, 0.60 * max_sharpe_risk), 'risk_desc': "60% risk line", 'fallback_corr': (0.20, 0.40), 'fallback_desc': "Fallback (Low Corr)"},
        {'risk_range': (0.00 * max_sharpe_risk, 0.40 * max_sharpe_risk), 'risk_desc': "40% risk line", 'fallback_corr': (-0.20, 0.20), 'fallback_desc': "Fallback (Neg Corr)"}
    ]

    for target in suggestion_targets:
        if len(suggestions) >= 3:
            break # Stop if we already have 3 suggestions

        found_suggestion = False

        # --- Attempt 1: Risk-Based Suggestion ---
        print(f"Attempting risk-based suggestion for {target['risk_desc']} (Risk: {target['risk_range'][0]:.4f}-{target['risk_range'][1]:.4f})")
        risk_candidates_potential = []
        for p in valid_candidates_pool:
            if not isinstance(p.get('combo'), list): 
                continue
            p_combo_tuple = tuple(sorted(p['combo']))
            if p_combo_tuple in used_combos: 
                continue

            candidate_risk = p.get(risk_key, p.get("risk"))
            if candidate_risk is not None and target['risk_range'][0] <= candidate_risk < target['risk_range'][1]:
                covariance_score = 1.0
                corr_score_val = p.get('correlation_score')
                if corr_score_val is not None and pd.notna(corr_score_val):
                     avg_corr = abs(corr_score_val)
                     covariance_score = 1.0 - min(avg_corr, 0.8)
                risk_proximity = 1.0 - abs(candidate_risk - 0.8 * target['risk_range'][1]) / target['risk_range'][1] if target['risk_range'][1] > 0 else 1.0
                score = (
                    p.get('return', 0) * 0.20 + p.get('sharpe', 0) * 0.10 +
                    p.get('mod_sharpe', 0) * 0.05 + p.get('sortino', 0) * 0.05 +
                    p.get('omega', 0) * 0.05 + p.get('calmar', 0) * 0.10 +
                    risk_proximity * 0.20 + covariance_score * 0.25
                )
                risk_candidates_potential.append({'score': score, 'candidate': p})

        if risk_candidates_potential:
            risk_candidates_potential.sort(key=lambda x: x['score'], reverse=True)
            # Iterate through sorted candidates to find one that meets the symbol count constraint
            for candidate_info in risk_candidates_potential:
                candidate = candidate_info['candidate']
                candidate_combo = candidate.get('combo', [])
                if not isinstance(candidate_combo, list): 
                    continue # Should not happen based on earlier check, but safety first

                # Check cumulative symbol count constraint
                temp_counts = cumulative_symbol_counts.copy()
                temp_counts.update(candidate_combo)
                valid_symbol_count = True
                for orig_sym in original_pairs_set:
                    if temp_counts[orig_sym] > 2: # Changed limit from 2 to 3
                        valid_symbol_count = False
                        print(f"  Skipping risk candidate {candidate_combo} due to symbol '{orig_sym}' count > 2")
                        break # Stop checking this candidate

                if valid_symbol_count:
                    # Found a valid candidate
                    best_candidate = candidate
                    best_candidate['risk_description'] = target['risk_desc']
                    best_candidate['used_risk_measure'] = risk_key
                    print(f"Found valid risk-based suggestion: {best_candidate['combo']} (Score: {candidate_info['score']:.4f})")
                    suggestions.append(best_candidate)
                    used_combos.add(tuple(sorted(best_candidate['combo'])))
                    cumulative_symbol_counts.update(best_candidate['combo']) # Update main counter
                    found_suggestion = True
                    break # Exit the inner loop (found suggestion for this target)


        # --- Attempt 2: Correlation-Based Fallback (if risk-based failed) ---
        if not found_suggestion:
            print(f"Risk-based failed or no valid candidate found. Attempting fallback: {target['fallback_desc']} (Corr: {target['fallback_corr'][0]:.2f}-{target['fallback_corr'][1]:.2f})")
            fallback_candidates_potential = []
            for p in valid_candidates_pool:
                 if not isinstance(p.get('combo'), list): 
                    continue
                 p_combo_tuple = tuple(sorted(p['combo']))
                 if p_combo_tuple in used_combos: 
                    continue

                 corr_score = p.get('correlation_score')
                 if corr_score is not None and pd.notna(corr_score) and target['fallback_corr'][0] <= corr_score < target['fallback_corr'][1]:
                     fallback_candidates_potential.append({'return': p.get('return', -float('inf')), 'candidate': p})

            if fallback_candidates_potential:
                fallback_candidates_potential.sort(key=lambda x: x['return'], reverse=True)
                # Iterate through sorted candidates to find one that meets the symbol count constraint
                for fallback_info in fallback_candidates_potential:
                    candidate = fallback_info['candidate']
                    candidate_combo = candidate.get('combo', [])
                    if not isinstance(candidate_combo, list): 
                        continue

                    # Check cumulative symbol count constraint
                    temp_counts = cumulative_symbol_counts.copy()
                    temp_counts.update(candidate_combo)
                    valid_symbol_count = True
                    for orig_sym in original_pairs_set:
                        if temp_counts[orig_sym] > 3: # Changed limit from 2 to 3
                            valid_symbol_count = False
                            print(f"  Skipping fallback candidate {candidate_combo} due to symbol '{orig_sym}' count > 2")
                            break

                    if valid_symbol_count:
                        # Found a valid fallback candidate
                        best_fallback = candidate
                        best_fallback['risk_description'] = target['fallback_desc']
                        best_fallback['used_risk_measure'] = risk_key
                        print(f"Found valid fallback suggestion: {best_fallback['combo']} (Corr: {best_fallback['correlation_score']:.4f}, Return: {fallback_info['return']:.4f})")
                        suggestions.append(best_fallback)
                        used_combos.add(tuple(sorted(best_fallback['combo'])))
                        cumulative_symbol_counts.update(best_fallback['combo']) # Update main counter
                        found_suggestion = True
                        break # Exit the inner loop

            if not found_suggestion: # Check again after iterating through fallbacks
                 print(f"No suitable fallback candidates found meeting constraints in range [{target['fallback_corr'][0]:.2f}, {target['fallback_corr'][1]:.2f}).")

        if not found_suggestion:
             print(f"Could not find any suggestion (risk or fallback) for target slot {len(suggestions) + 1}.")


    # Final processing for MPT strings (only for successfully added suggestions)
    for suggestion in suggestions:
        if isinstance(suggestion.get('combo'), list) and isinstance(suggestion.get('weights'), list):
            allocation_strings = [f"{sym}:{weight:.4f}" for sym, weight in zip(suggestion['combo'], suggestion['weights'])]
            suggestion['mpt_string'] = ",".join(allocation_strings)
        else:
            suggestion['mpt_string'] = "Error: Invalid combo/weights"
    
    print(f"Generated {len(suggestions)} suggestions")
    return suggestions[:3]  # Ensure we return at most 3 suggestions

def efficient_frontier_upper_hull(candidates):
    points = [(c['risk'], c['return']) for c in candidates]
    if len(points) < 2:
        return points
    
    # Sort by risk ascending, then return ascending
    points = sorted(points, key=lambda x: (x[0], x[1]))
    
    def cross(o, a, b):
        """Cross product of OA x OB > 0 => counter-clockwise turn."""
        return (a[0] - o[0])*(b[1] - o[1]) - (a[1] - o[1])*(b[0] - o[0])
    
    # Build lower hull (we won't actually need it, but let's keep for completeness)
    lower = []
    for p in points:
        while len(lower) >= 2 and cross(lower[-2], lower[-1], p) <= 0:
            lower.pop()
        lower.append(p)
    
    # Build upper hull
    upper = []
    for p in reversed(points):
        while len(upper) >= 2 and cross(upper[-2], upper[-1], p) <= 0:
            upper.pop()
        upper.append(p)
    
    # Return the upper hull points in correct order
    return list(reversed(upper))[1:]  # skip the last to avoid duplication

# Add this function to identify portfolios on the efficient frontier hull
def identify_candidates_on_hull(candidates, hull_points, type='risk'):
    """
    Identifies which candidates lie on the efficient frontier hull
    
    Args:
        candidates: List of portfolio candidates
        hull_points: List of (x, y) points on the hull
        type: 'risk' for standard frontier, 'cvar' for CVaR frontier
    
    Returns:
        List of candidates that lie on the hull
    """
    hull_candidates = []
    x_key = 'risk' if type == 'risk' else 'cvar_95'
    
    for candidate in candidates:
        # Skip candidates that don't have required metrics
        if x_key not in candidate or 'return' not in candidate:
            continue
            
        point = (candidate[x_key], candidate['return'])
        
        # Check if this point is on the hull (within a small tolerance)
        for hull_pt in hull_points:
            if (abs(point[0] - hull_pt[0]) < 0.0001 and 
                abs(point[1] - hull_pt[1]) < 0.0001):
                hull_candidates.append(candidate)
                break
                
    return hull_candidates

# Add this function to calculate portfolio correlations
def calculate_portfolio_correlation(portfolio1, portfolio2, returns_df):
    """
    Calculate correlation between two portfolios
    
    Args:
        portfolio1: Dict with 'combo' and 'weights'
        portfolio2: Dict with 'combo' and 'weights'
        returns_df: DataFrame of returns for all symbols
    
    Returns:
        Correlation coefficient
    """
    # Create portfolio return series for both portfolios
    port1_weights = {sym: w for sym, w in zip(portfolio1['combo'], portfolio1['weights'])}
    port2_weights = {sym: w for sym, w in zip(portfolio2['combo'], portfolio2['weights'])}
    
    # Calculate portfolio returns
    port1_return = pd.Series(0, index=returns_df.index)
    for sym, weight in port1_weights.items():
        if sym in returns_df.columns:
            port1_return += returns_df[sym] * weight
            
    port2_return = pd.Series(0, index=returns_df.index)
    for sym, weight in port2_weights.items():
        if sym in returns_df.columns:
            port2_return += returns_df[sym] * weight
    
    # Calculate correlation
    return port1_return.corr(port2_return)

def find_recommended_portfolios(candidates, returns_df, hull_points, type='risk'):
    """
    Find four recommended portfolios:
    1. Best returns on frontier hull
    2. Best Sharpe/CF Sharpe/Calmar with low correlation to 1st
    3. Average returns but low correlation to 1st (hedge)
    4. Portfolio at approximately 75% of the frontier length (instead of 50%)
    
    Args:
        candidates: List of all portfolio candidates
        returns_df: DataFrame of asset returns
        hull_points: Points on the efficient frontier hull
        type: 'risk' for standard frontier, 'cvar' for CVaR frontier
    
    Returns:
        Tuple of (best_returns, best_metric, best_hedge, mid_frontier) portfolios
    """
    # Get candidates on the hull
    hull_candidates = identify_candidates_on_hull(candidates, hull_points, type)
    
    if not hull_candidates:
        return None, None, None, None
    
    # 1. Find portfolio with best returns on the hull
    best_returns = max(hull_candidates, key=lambda x: x['return'])
    
    # 2. Find portfolio with best sharpe/cf_sharpe/calmar that has low correlation to best_returns
    metric_candidates = []
    
    for candidate in hull_candidates:
        if candidate == best_returns:
            continue
            
        # Calculate correlation with best_returns
        corr = calculate_portfolio_correlation(candidate, best_returns, returns_df)
        
        # Only consider candidates with correlation <= 0.30
        if abs(corr) <= 0.30:
            # Store correlation for later use
            candidate['correlation_to_best'] = corr
            metric_candidates.append(candidate)
    
    # Find best candidate based on metrics (try different metrics in order)
    best_metric = None
    if metric_candidates:
        # Try CF Sharpe first
        cf_candidates = [c for c in metric_candidates if 'mod_sharpe' in c and c['mod_sharpe'] > 0]
        if cf_candidates:
            best_metric = max(cf_candidates, key=lambda x: x['mod_sharpe'])

        # If no good CF Sharpe, try regular Sharpe
        if not best_metric:
            sharpe_candidates = [c for c in metric_candidates if 'sharpe' in c and c['sharpe'] > 0]
            if sharpe_candidates:
                best_metric = max(sharpe_candidates, key=lambda x: x['sharpe'])
        
        # If still no good candidate, try Calmar
        if not best_metric:
            calmar_candidates = [c for c in metric_candidates if 'calmar' in c and c['calmar'] > 0]
            if calmar_candidates:
                best_metric = max(calmar_candidates, key=lambda x: x['calmar'])
    
    # 3. Find hedge portfolio (average returns but lowest correlation to best_returns)
    avg_return = np.mean([c['return'] for c in candidates if 'return' in c])
    
    hedge_candidates = []
    for candidate in candidates:
        if candidate == best_returns or candidate == best_metric:
            continue
            
        # Calculate correlation with best_returns
        corr = calculate_portfolio_correlation(candidate, best_returns, returns_df)
        
        # Store correlation for later use
        candidate['correlation_to_best'] = corr
        
        # Consider candidates that have returns within 50% of average
        if 'return' in candidate and abs(candidate['return'] - avg_return) < 0.5 * avg_return:
            hedge_candidates.append(candidate)
    
    best_hedge = None
    if hedge_candidates:
        # Find candidate with lowest absolute correlation to best_returns
        best_hedge = min(hedge_candidates, key=lambda x: abs(x['correlation_to_best']))
    
    # 4. Find portfolio at approximately 75% of the frontier length (changed from 50%)
    mid_portfolio = None
    if len(hull_points) >= 3:
        # Sort hull points by risk (x-value)
        sorted_hull = sorted(hull_points, key=lambda p: p[0])
        
        # Find the total length of the frontier in terms of risk
        min_risk = sorted_hull[0][0]
        max_risk = sorted_hull[-1][0]
        frontier_length = max_risk - min_risk
        
        # Target risk at 75% of the frontier length (changed from 50%)
        target_risk = min_risk + (frontier_length * 0.75)
        
        # Find the candidate closest to this target risk
        # Make sure we're using candidates that are on or very close to the hull
        mid_frontier_candidates = []
        for candidate in hull_candidates:  # Use hull_candidates to ensure it's on the frontier
            x_key = 'risk' if type == 'risk' else 'cvar_95'
            if x_key in candidate:
                # Calculate distance from target risk
                distance = abs(candidate[x_key] - target_risk)
                mid_frontier_candidates.append((distance, candidate))
        
        # Sort by distance
        mid_frontier_candidates.sort(key=lambda x: x[0])
        
        # Get the closest portfolio to our target
        if mid_frontier_candidates:
            mid_portfolio = mid_frontier_candidates[0][1]
    
    return best_returns, best_metric, best_hedge, mid_portfolio

def combine_portfolios(portfolios, weights=None):
    """
    Combine multiple portfolios into a single portfolio with weighted allocations.
    Enhanced to handle portfolios that may be simplified or incomplete.
    """
    # Add defensive checks
    if not portfolios or len(portfolios) == 0:
        print("No portfolios to combine")
        return None
    
    # Use equal weights if none provided
    if weights is None:
        weights = [1.0/len(portfolios)] * len(portfolios)
    elif len(weights) != len(portfolios):
        print("Warning: Number of weights doesn't match portfolios, using equal weights")
        weights = [1.0/len(portfolios)] * len(portfolios)
        
    print(f"Combining {len(portfolios)} portfolios with weights: {weights}")
    
    # Create a new portfolio with default values
    combined = {
        "combo": [],
        "weights": [],
        "optimization": "Combined Portfolio",
        "return": 0,
        "risk": 0,
        "sharpe": 0,
        "sortino": 0,
        "omega": 0,
        "calmar": 0,
        "mod_sharpe": 0
    }
    
    # Create a dictionary to accumulate weights for each symbol
    symbol_weights = {}
    
    # Process each portfolio
    for i, (port, weight) in enumerate(zip(portfolios, weights)):
        print(f"Processing portfolio {i}")
        
        # Sanity check - ensure this is a valid portfolio with minimum required fields
        if not isinstance(port, dict) or 'combo' not in port or 'weights' not in port:
            print(f"Invalid portfolio object: {type(port)}")
            continue
            
        # Get the portfolio's combo and weights
        combo = port.get('combo', [])
        port_weights = port.get('weights', [])
        
        # Another sanity check - ensure combos and weights match
        if len(combo) != len(port_weights):
            print(f"Portfolio {i} has mismatched combo/weights: {len(combo)} symbols and {len(port_weights)} weights")
            continue
        
        # Add the symbols and scaled weights to our accumulator
        for sym, w in zip(combo, port_weights):
            if sym in symbol_weights:
                symbol_weights[sym] += w * weight
            else:
                symbol_weights[sym] = w * weight
                
        # Also accumulate metrics with the same weights
        for metric in ['return', 'risk', 'sharpe', 'sortino', 'omega', 'calmar', 'mod_sharpe']:
            if metric in port and port.get(metric) is not None:
                if combined.get(metric) is None:
                    combined[metric] = 0
                combined[metric] += port.get(metric, 0) * weight
    
    # Convert symbol_weights to lists for combo and weights
    combined["combo"] = list(symbol_weights.keys())
    combined["weights"] = list(symbol_weights.values())
    
    # If we didn't get any valid portfolios, return None
    if not combined["combo"]:
        print("No valid symbols found in portfolios")
        return None
        
    print(f"Created combined portfolio with {len(combined['combo'])} symbols")
    return combined

def process_custom_portfolio(custom_portfolio, returns_df, current_day_only=False):
    """
    Given a custom portfolio (with keys "combo" and "weights") and returns DataFrame,
    compute portfolio metrics using calculate_portfolio_metrics.
    
    Args:
        custom_portfolio: Dict with 'combo' and 'weights' keys
        returns_df: DataFrame of returns for all symbols
        current_day_only: If True, only use today's data for metric calculation
    """
    combo = custom_portfolio.get("combo", [])
    weights = custom_portfolio.get("weights", [])
    if not combo or not weights:
        raise ValueError("Custom portfolio must include 'combo' and 'weights'")
    
    # Ensure you only use symbols that exist in the returns DataFrame.
    valid_symbols = [sym for sym in combo if sym in returns_df.columns]
    if not valid_symbols:
        raise ValueError("None of the portfolio symbols have market data.")
    
    # Filter to current day only if requested
    if current_day_only:
        # Get today's date - use the current time in UTC
        today = pd.Timestamp.now(tz='UTC').floor('D')
        # Filter returns_df to only include data from today
        today_returns_df = returns_df[returns_df.index.date == today.date()]
        # If no data for today, use the most recent day's data
        if today_returns_df.empty:
            last_date = returns_df.index.date[-1]
            today_returns_df = returns_df[returns_df.index.date == last_date]
            print(f"No data for today, using most recent date: {last_date}")

        # Use this filtered DataFrame instead
        calc_df = today_returns_df
        print(f"Using {len(calc_df)} rows of today's data for metric calculation")

        # For today's metrics, calculate actual portfolio performance
        if len(calc_df) > 0:
            # Calculate actual portfolio returns for today
            portfolio_returns = calc_df[valid_symbols].dot(weights[:len(valid_symbols)])

            # Calculate today's metrics directly from actual returns
            actual_return = portfolio_returns.sum()  # Sum of today's returns
            actual_risk = portfolio_returns.std() if len(portfolio_returns) > 1 else 0.0
            actual_sharpe = actual_return / actual_risk if actual_risk > 0 else 0.0

            # Create simplified metrics for today
            metrics = {
                'return': float(actual_return),
                'risk': float(actual_risk),
                'sharpe': float(actual_sharpe),
                'sortino': 0.0,  # Not meaningful for single day
                'omega': 0.0,    # Not meaningful for single day
                'calmar': 0.0,   # Not meaningful for single day
                'mod_sharpe': 0.0  # Not meaningful for single day
            }
        else:
            # No data available
            metrics = {
                'return': 0.0,
                'risk': 0.0,
                'sharpe': 0.0,
                'sortino': 0.0,
                'omega': 0.0,
                'calmar': 0.0,
                'mod_sharpe': 0.0
            }
    else:
        # Use the full DataFrame for historical metrics
        calc_df = returns_df

        # Compute metrics using the standard approach
        # Convert log returns to arithmetic returns for portfolio optimization
        arithmetic_calc_df = convert_log_to_arithmetic_returns(calc_df[valid_symbols])
        mean_returns = arithmetic_calc_df.mean()
        cov_matrix = arithmetic_calc_df.cov()
        # For now, use M15 timeframe as default and don't annualize in portfolio processing
        # The annualization will be handled at the display level
        metrics = calculate_portfolio_metrics(weights[:len(valid_symbols)], mean_returns, cov_matrix, calc_df[valid_symbols])
    
    # Merge the computed metrics into the custom portfolio dictionary
    custom_portfolio.update(metrics)
    
    # Add a flag indicating whether this is using current day data
    custom_portfolio['current_day_metrics'] = current_day_only
    
    return custom_portfolio