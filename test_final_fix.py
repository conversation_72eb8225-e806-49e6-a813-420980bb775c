#!/usr/bin/env python3
"""
Final test to confirm MaxModSharpe fix is working
"""

import numpy as np
import pandas as pd
from portfolio_optimizer_rust import process_combo_rust

def test_maxmodsharpe_fix():
    """Test that MaxModSharpe is now working correctly"""
    print("🔍 Final Test: MaxModSharpe Fix Verification")
    print("=" * 50)
    
    # Test 1: Data that should work (balanced returns)
    print("--- Test 1: Balanced Returns Data ---")
    np.random.seed(100)
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    periods = 200
    
    returns_data = {}
    returns_data['EURUSD'] = np.random.normal(0.002, 0.015, periods)   # Positive
    returns_data['GBPUSD'] = np.random.normal(0.001, 0.018, periods)   # Positive  
    returns_data['USDJPY'] = np.random.normal(0.0015, 0.012, periods)  # Positive
    
    returns_df = pd.DataFrame(returns_data)
    combo = (tuple(symbols), returns_df, returns_df)
    
    mean_returns = returns_df.mean().values
    print(f"Mean returns: {mean_returns}")
    print(f"All positive: {np.all(mean_returns > 0)}")
    
    results = process_combo_rust([combo])
    
    optimization_counts = {}
    for result_type, result_dict in results:
        opt_name = result_dict.get('optimization', 'Unknown')
        optimization_counts[opt_name] = optimization_counts.get(opt_name, 0) + 1
    
    maxmod_count = optimization_counts.get('Max CF Sharpe', 0)
    print(f"MaxModSharpe results: {maxmod_count}")
    
    if maxmod_count > 0:
        print("✅ MaxModSharpe working with balanced data!")
        # Show sample result
        maxmod_results = [r for r in results if r[1].get('optimization') == 'Max CF Sharpe']
        sample = maxmod_results[0][1]
        weights = sample.get('weights', [])
        min_weight = min(abs(w) for w in weights) if weights else 0
        print(f"  Sample weights: {[f'{w:.4f}' for w in weights]}")
        print(f"  Min |weight|: {min_weight:.6f}")
        print(f"  Return: {sample.get('return', 0):.6f}")
        print(f"  Risk: {sample.get('risk', 0):.6f}")
    else:
        print("❌ MaxModSharpe still not working with balanced data!")
    
    print()
    
    # Test 2: Data with mixed returns (the problematic case)
    print("--- Test 2: Mixed Returns Data (Problematic Case) ---")
    np.random.seed(300)
    
    returns_data = {}
    returns_data['EURUSD'] = np.random.normal(0.002, 0.015, periods)   # Positive
    returns_data['GBPUSD'] = np.random.normal(-0.001, 0.018, periods)  # Negative
    returns_data['USDJPY'] = np.random.normal(0.0005, 0.012, periods)  # Small positive
    
    returns_df = pd.DataFrame(returns_data)
    combo = (tuple(symbols), returns_df, returns_df)
    
    mean_returns = returns_df.mean().values
    print(f"Mean returns: {mean_returns}")
    print(f"Has negative returns: {np.any(mean_returns < 0)}")
    
    results = process_combo_rust([combo])
    
    optimization_counts = {}
    for result_type, result_dict in results:
        opt_name = result_dict.get('optimization', 'Unknown')
        optimization_counts[opt_name] = optimization_counts.get(opt_name, 0) + 1
    
    maxmod_count = optimization_counts.get('Max CF Sharpe', 0)
    print(f"MaxModSharpe results: {maxmod_count}")
    
    if maxmod_count > 0:
        print("✅ MaxModSharpe working even with mixed returns!")
    else:
        print("❌ MaxModSharpe filtered out due to weight threshold (expected)")
        print("   This is mathematically correct behavior - the optimal portfolio")
        print("   assigns very small weights to assets with negative returns.")
    
    # Show all optimization results for comparison
    print(f"\nAll optimization results:")
    expected_optimizations = [
        'Min Variance',
        'Max Sharpe', 
        'Max Sortino',
        'Max Omega',
        'Max Calmar',
        'Max CF Sharpe'
    ]
    
    for opt_name in expected_optimizations:
        count = optimization_counts.get(opt_name, 0)
        status = "✅" if count > 0 else "❌"
        print(f"  {status} {opt_name}: {count}")
    
    print()
    
    # Test 3: Verify the Rust optimization is actually working
    print("--- Test 3: Direct Rust Optimization Verification ---")
    import ratio_calcs_rust
    
    mean_returns = returns_df.mean().values
    cov_matrix = returns_df.cov().values
    returns_matrix = returns_df.values.T
    
    try:
        weights, success, objective = ratio_calcs_rust.optimize_portfolio_rust(
            mean_returns.astype(np.float64),
            cov_matrix.astype(np.float64),
            returns_matrix.astype(np.float64),
            'max_modified_sharpe',
            np.array([-1.0, -1.0, -1.0]),
            np.array([1.0, 1.0, 1.0]),
            200
        )
        
        print(f"Direct Rust optimization:")
        print(f"  Success: {success}")
        print(f"  Objective: {objective:.10f}")
        print(f"  Weights: {weights}")
        print(f"  Weights sum: {np.sum(weights):.10f}")
        
        if success:
            min_weight = min(abs(w) for w in weights)
            portfolio_return = np.dot(mean_returns, weights)
            print(f"  Min |weight|: {min_weight:.6f}")
            print(f"  Portfolio return: {portfolio_return:.6f}")
            print(f"  Passes 0.05 threshold: {min_weight >= 0.05}")
            print(f"  Return >= 0: {portfolio_return >= 0}")
            
            if min_weight < 0.05:
                print(f"  💡 Optimal solution has small weights - this is correct!")
                print(f"     The optimizer correctly minimizes exposure to negative-return assets.")
            
            if success and min_weight >= 0.05 and portfolio_return >= 0:
                print(f"  ✅ Would pass all filters!")
            else:
                print(f"  ❌ Would be filtered (but optimization is working correctly)")
        
    except Exception as e:
        print(f"❌ Direct optimization error: {e}")
        import traceback
        traceback.print_exc()

def test_comparison_with_other_optimizations():
    """Compare MaxModSharpe behavior with other optimization types"""
    print("\n🔍 Comparison with Other Optimization Types")
    print("=" * 50)
    
    # Use the same problematic data
    np.random.seed(300)
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    periods = 200
    
    returns_data = {}
    returns_data['EURUSD'] = np.random.normal(0.002, 0.015, periods)
    returns_data['GBPUSD'] = np.random.normal(-0.001, 0.018, periods)
    returns_data['USDJPY'] = np.random.normal(0.0005, 0.012, periods)
    
    returns_df = pd.DataFrame(returns_data)
    mean_returns = returns_df.mean().values
    cov_matrix = returns_df.cov().values
    returns_matrix = returns_df.values.T
    
    import ratio_calcs_rust
    
    optimization_types = [
        'max_sharpe',
        'max_sortino', 
        'max_omega',
        'max_calmar',
        'max_modified_sharpe'
    ]
    
    print("Testing all optimization types with the same problematic data:")
    print(f"Mean returns: {mean_returns}")
    print()
    
    for opt_type in optimization_types:
        try:
            weights, success, objective = ratio_calcs_rust.optimize_portfolio_rust(
                mean_returns.astype(np.float64),
                cov_matrix.astype(np.float64),
                returns_matrix.astype(np.float64),
                opt_type,
                np.array([-1.0, -1.0, -1.0]),
                np.array([1.0, 1.0, 1.0]),
                200
            )
            
            if success:
                min_weight = min(abs(w) for w in weights)
                portfolio_return = np.dot(mean_returns, weights)
                passes_threshold = min_weight >= 0.05
                positive_return = portfolio_return >= 0
                
                status = "✅" if passes_threshold and positive_return else "❌"
                print(f"{status} {opt_type}:")
                print(f"    Weights: {[f'{w:.4f}' for w in weights]}")
                print(f"    Min |weight|: {min_weight:.6f}")
                print(f"    Portfolio return: {portfolio_return:.6f}")
                print(f"    Passes filters: {passes_threshold and positive_return}")
            else:
                print(f"❌ {opt_type}: Optimization failed")
                
        except Exception as e:
            print(f"❌ {opt_type}: Error - {e}")
        
        print()

if __name__ == "__main__":
    test_maxmodsharpe_fix()
    test_comparison_with_other_optimizations()
