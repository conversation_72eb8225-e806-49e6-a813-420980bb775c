#!/usr/bin/env python3
"""
Debug the specific case with mixed positive/negative returns where MaxModSharpe fails
"""

import numpy as np
import pandas as pd
from portfolio_optimizer_rust import process_combo_rust, RustPortfolioOptimizer, normalize_weights, calculate_portfolio_metrics
import ratio_calcs_rust

def debug_mixed_returns_case():
    """Debug the exact case where MaxModSharpe fails"""
    print("🔍 Debugging Mixed Returns Case")
    print("=" * 35)
    
    # Recreate the exact same data
    np.random.seed(300)
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    periods = 300
    
    returns_data = {}
    returns_data['EURUSD'] = np.random.normal(0.002, 0.015, periods)   # Positive
    returns_data['GBPUSD'] = np.random.normal(-0.001, 0.018, periods)  # Negative
    returns_data['USDJPY'] = np.random.normal(0.0005, 0.012, periods)  # Small positive
    
    returns_df = pd.DataFrame(returns_data)
    mean_returns = returns_df.mean().values
    cov_matrix = returns_df.cov().values
    returns_matrix = returns_df.values.T
    
    print(f"Mean returns: {mean_returns}")
    print(f"Cov matrix shape: {cov_matrix.shape}")
    print()
    
    # Step 1: Test direct Rust optimization
    print("--- Step 1: Direct Rust Optimization ---")
    try:
        weights, success, obj_value = ratio_calcs_rust.optimize_portfolio_rust(
            mean_returns.astype(np.float64),
            cov_matrix.astype(np.float64),
            returns_matrix.astype(np.float64),
            'max_modified_sharpe',
            np.array([-1.0, -1.0, -1.0]),
            np.array([1.0, 1.0, 1.0]),
            200
        )
        
        print(f"Success: {success}")
        print(f"Objective: {obj_value}")
        print(f"Raw weights: {weights}")
        print(f"Weights sum: {np.sum(weights):.6f}")
        
        if success:
            # Step 2: Weight normalization
            print(f"\n--- Step 2: Weight Normalization ---")
            normalized_weights = normalize_weights(weights)
            print(f"Normalized weights: {normalized_weights}")
            print(f"Normalized sum: {np.sum(normalized_weights):.6f}")
            
            # Step 3: Weight threshold check
            print(f"\n--- Step 3: Weight Threshold Check ---")
            abs_weights = np.abs(normalized_weights)
            min_weight = np.min(abs_weights)
            passes_threshold = np.all(abs_weights >= 0.05)
            
            print(f"Absolute weights: {abs_weights}")
            print(f"Min absolute weight: {min_weight:.6f}")
            print(f"Passes 0.05 threshold: {passes_threshold}")
            
            if not passes_threshold:
                print(f"❌ FILTERED OUT: Weight threshold failed!")
                print(f"Weights below threshold: {abs_weights[abs_weights < 0.05]}")
                return
            
            # Step 4: Portfolio metrics calculation
            print(f"\n--- Step 4: Portfolio Metrics ---")
            try:
                metrics = calculate_portfolio_metrics(normalized_weights, mean_returns, cov_matrix, returns_df)
                
                portfolio_return = metrics.get('return', 0)
                portfolio_risk = metrics.get('risk', 0)
                
                print(f"Portfolio return: {portfolio_return:.6f}")
                print(f"Portfolio risk: {portfolio_risk:.6f}")
                print(f"Return >= 0: {portfolio_return >= 0}")
                
                if portfolio_return < 0:
                    print(f"❌ FILTERED OUT: Negative return!")
                    return
                
                print(f"✅ Should be included in results!")
                print(f"All metrics: {metrics}")
                
            except Exception as e:
                print(f"❌ ERROR in metrics calculation: {e}")
                import traceback
                traceback.print_exc()
                return
            
        else:
            print(f"❌ OPTIMIZATION FAILED!")
            return
            
    except Exception as e:
        print(f"❌ ERROR in optimization: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Step 5: Test through the full pipeline
    print(f"\n--- Step 5: Full Pipeline Test ---")
    combo = (tuple(symbols), returns_df, returns_df)
    results = process_combo_rust([combo])
    
    print(f"Total results: {len(results)}")
    
    # Analyze results
    optimization_counts = {}
    for result_type, result_dict in results:
        opt_name = result_dict.get('optimization', 'Unknown')
        optimization_counts[opt_name] = optimization_counts.get(opt_name, 0) + 1
    
    print(f"Results by optimization:")
    for opt_name in ['Min Variance', 'Max Sharpe', 'Max Sortino', 'Max Omega', 'Max Calmar', 'Max CF Sharpe']:
        count = optimization_counts.get(opt_name, 0)
        status = "✅" if count > 0 else "❌"
        print(f"  {status} {opt_name}: {count}")
    
    maxmod_count = optimization_counts.get('Max CF Sharpe', 0)
    if maxmod_count == 0:
        print(f"\n❌ MaxModSharpe missing from pipeline results!")
        print(f"💡 There's a discrepancy between direct optimization and pipeline")
        
        # Let's manually trace through the pipeline logic
        print(f"\n--- Manual Pipeline Trace ---")
        trace_pipeline_logic(combo)
    else:
        print(f"\n✅ MaxModSharpe working in pipeline!")

def trace_pipeline_logic(combo):
    """Manually trace through the pipeline logic to find the issue"""
    print("Tracing pipeline logic step by step...")
    
    symbols, adjusted, _ = combo
    
    # Check basic data validity (from process_combo_rust)
    if not set(symbols).issubset(adjusted.columns):
        print("❌ Symbols not in dataframe columns")
        return
    
    # Extract data
    adjusted = adjusted[list(symbols)]
    mean_returns = adjusted.mean().values
    cov_matrix = adjusted.cov().values
    
    # Check for invalid data
    if np.any(np.isnan(cov_matrix)) or np.any(np.isinf(cov_matrix)):
        print("❌ Invalid covariance matrix")
        return
    if np.any(np.isnan(mean_returns)) or np.any(np.isinf(mean_returns)):
        print("❌ Invalid mean returns")
        return
    
    print("✅ Data validation passed")
    
    # Prepare returns matrix
    returns_matrix = adjusted.values.T
    
    # Test each optimization type manually
    optimization_configs = [
        ('min_variance', 'Min Variance', ['minvar', 'composite']),
        ('max_sharpe', 'Max Sharpe', ['maxsharpe', 'composite', 'ss_composite']),
        ('max_sortino', 'Max Sortino', ['maxsortino', 'ss_composite']),
        ('max_omega', 'Max Omega', ['maxomega']),
        ('max_calmar', 'Max Calmar', ['maxcalmar']),
        ('max_modified_sharpe', 'Max CF Sharpe', ['maxmodsharpe']),
    ]
    
    optimizer = RustPortfolioOptimizer()
    
    for opt_type, opt_name, tags in optimization_configs:
        print(f"\nTesting {opt_name} ({opt_type}):")
        
        try:
            weights, success, objective_value = optimizer.optimize_single_portfolio(
                mean_returns=mean_returns,
                cov_matrix=cov_matrix,
                returns_matrix=returns_matrix,
                optimization_type=opt_type,
                bounds=(-1.0, 1.0),
                max_iterations=200
            )
            
            print(f"  Optimization success: {success}")
            
            if success:
                # Normalize weights
                weights = normalize_weights(weights)
                
                # Check minimum weight threshold
                if np.all(np.abs(weights) >= 0.05):
                    # Calculate comprehensive metrics
                    metrics = calculate_portfolio_metrics(weights, mean_returns, cov_matrix, adjusted)
                    
                    # Filter out portfolios with negative returns
                    if metrics.get('return', 0) >= 0:
                        print(f"  ✅ PASSED all filters")
                        print(f"  Return: {metrics.get('return', 0):.6f}")
                        print(f"  Risk: {metrics.get('risk', 0):.6f}")
                        print(f"  Min weight: {np.min(np.abs(weights)):.6f}")
                    else:
                        print(f"  ❌ FILTERED: Negative return ({metrics.get('return', 0):.6f})")
                else:
                    min_weight = np.min(np.abs(weights))
                    print(f"  ❌ FILTERED: Weight threshold ({min_weight:.6f} < 0.05)")
            else:
                print(f"  ❌ FAILED: Optimization unsuccessful")
                
        except Exception as e:
            print(f"  ❌ ERROR: {e}")

if __name__ == "__main__":
    debug_mixed_returns_case()
