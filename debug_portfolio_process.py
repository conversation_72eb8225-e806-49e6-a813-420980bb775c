#!/usr/bin/env python3
"""
Debug the exact portfolio processing pipeline to find where MaxModSharpe results are lost
"""

import numpy as np
import pandas as pd
from portfolio_optimizer_rust import process_combo_rust, RustPortfolioOptimizer

def debug_portfolio_pipeline():
    """Debug the complete portfolio processing pipeline"""
    print("🔍 Debugging Portfolio Processing Pipeline")
    print("=" * 50)
    
    # Create test data similar to real usage
    np.random.seed(42)
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    periods = 300
    
    returns_data = {}
    for symbol in symbols:
        returns_data[symbol] = np.random.normal(0.001, 0.015, periods)
    
    returns_df = pd.DataFrame(returns_data)
    
    # Create a combo tuple like the real process uses
    combo = (tuple(symbols), returns_df, returns_df)
    
    print(f"Test combo: {len(symbols)} assets, {periods} periods")
    print(f"Symbols: {symbols}")
    print()
    
    # Process using the real function
    print("--- Processing with process_combo_rust ---")
    results = process_combo_rust([combo])
    
    print(f"Total results: {len(results)}")
    
    # Analyze results by optimization type
    optimization_counts = {}
    for result_type, result_dict in results:
        opt_name = result_dict.get('optimization', 'Unknown')
        optimization_counts[opt_name] = optimization_counts.get(opt_name, 0) + 1
    
    print(f"\nResults by optimization type:")
    expected_optimizations = [
        'Min Variance',
        'Max Sharpe', 
        'Max Sortino',
        'Max Omega',
        'Max Calmar',
        'Max CF Sharpe'
    ]
    
    for opt_name in expected_optimizations:
        count = optimization_counts.get(opt_name, 0)
        status = "✅" if count > 0 else "❌"
        print(f"  {status} {opt_name}: {count}")
    
    # If MaxModSharpe is missing, debug step by step
    maxmod_count = optimization_counts.get('Max CF Sharpe', 0)
    if maxmod_count == 0:
        print(f"\n❌ MaxModSharpe missing! Debugging step by step...")
        debug_step_by_step(combo)
    else:
        print(f"\n✅ MaxModSharpe working correctly!")
        # Show sample results
        maxmod_results = [r for r in results if r[1].get('optimization') == 'Max CF Sharpe']
        for i, (result_type, result_dict) in enumerate(maxmod_results[:2]):
            weights = result_dict.get('weights', [])
            ret = result_dict.get('return', 0)
            risk = result_dict.get('risk', 0)
            print(f"  Sample {i+1}: Return={ret:.6f}, Risk={risk:.6f}, Weights={[f'{w:.3f}' for w in weights]}")

def debug_step_by_step(combo):
    """Debug the portfolio processing step by step"""
    print("🔍 Step-by-Step Debug")
    print("=" * 25)
    
    symbols, adjusted, _ = combo
    
    # Step 1: Data preparation
    mean_returns = adjusted.mean().values
    cov_matrix = adjusted.cov().values
    returns_matrix = adjusted.values.T  # Transpose for Rust
    
    print(f"Step 1 - Data preparation:")
    print(f"  Mean returns: {mean_returns}")
    print(f"  Cov matrix shape: {cov_matrix.shape}")
    print(f"  Returns matrix shape: {returns_matrix.shape}")
    print(f"  Data valid: {np.all(np.isfinite(mean_returns)) and np.all(np.isfinite(cov_matrix))}")
    print()
    
    # Step 2: Test MaxModSharpe optimization directly
    print(f"Step 2 - Direct MaxModSharpe optimization:")
    optimizer = RustPortfolioOptimizer()
    
    try:
        weights, success, obj_value = optimizer.optimize_single_portfolio(
            mean_returns=mean_returns,
            cov_matrix=cov_matrix,
            returns_matrix=returns_matrix,
            optimization_type='max_modified_sharpe',
            bounds=(-1.0, 1.0),
            max_iterations=200
        )
        
        print(f"  Success: {success}")
        print(f"  Objective: {obj_value}")
        print(f"  Raw weights: {weights}")
        print(f"  Weights sum: {np.sum(weights):.6f}")
        
        if success:
            # Step 3: Weight normalization
            from portfolio_optimizer_rust import normalize_weights
            normalized_weights = normalize_weights(weights)
            print(f"  Normalized weights: {normalized_weights}")
            print(f"  Normalized sum: {np.sum(normalized_weights):.6f}")
            
            # Step 4: Weight threshold check
            abs_weights = np.abs(normalized_weights)
            min_weight = np.min(abs_weights)
            passes_threshold = np.all(abs_weights >= 0.05)
            
            print(f"  Min absolute weight: {min_weight:.6f}")
            print(f"  Passes 0.05 threshold: {passes_threshold}")
            
            if not passes_threshold:
                print(f"  ❌ FILTERED OUT: Weight threshold failed!")
                return
            
            # Step 5: Portfolio metrics calculation
            from portfolio_optimizer_rust import calculate_portfolio_metrics
            metrics = calculate_portfolio_metrics(normalized_weights, mean_returns, cov_matrix, adjusted)
            
            portfolio_return = metrics.get('return', 0)
            print(f"  Portfolio return: {portfolio_return:.6f}")
            print(f"  Return >= 0: {portfolio_return >= 0}")
            
            if portfolio_return < 0:
                print(f"  ❌ FILTERED OUT: Negative return!")
                return
            
            print(f"  ✅ Should be included in results!")
            
        else:
            print(f"  ❌ OPTIMIZATION FAILED!")
            
    except Exception as e:
        print(f"  ❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

def test_multiple_combos():
    """Test with multiple different combinations to see if it's data-specific"""
    print(f"\n🔍 Testing Multiple Data Combinations")
    print("=" * 40)
    
    test_cases = [
        ("Case 1: 3 assets, normal", ['EURUSD', 'GBPUSD', 'USDJPY'], 42),
        ("Case 2: 2 assets, strong trend", ['EURUSD', 'GBPUSD'], 100),
        ("Case 3: 4 assets, mixed", ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'], 200),
        ("Case 4: 3 assets, low vol", ['EURUSD', 'GBPUSD', 'USDJPY'], 300),
    ]
    
    all_combos = []
    
    for name, symbols, seed in test_cases:
        np.random.seed(seed)
        periods = 250
        
        returns_data = {}
        for i, symbol in enumerate(symbols):
            # Vary the characteristics
            mean_ret = 0.001 * (i - len(symbols)/2)
            vol = 0.015 + 0.005 * (i % 2)
            returns_data[symbol] = np.random.normal(mean_ret, vol, periods)
        
        returns_df = pd.DataFrame(returns_data)
        combo = (tuple(symbols), returns_df, returns_df)
        all_combos.append(combo)
        
        print(f"{name}: {len(symbols)} assets")
    
    print(f"\nProcessing {len(all_combos)} combinations...")
    results = process_combo_rust(all_combos)
    
    print(f"Total results: {len(results)}")
    
    # Count by optimization type
    optimization_counts = {}
    for result_type, result_dict in results:
        opt_name = result_dict.get('optimization', 'Unknown')
        optimization_counts[opt_name] = optimization_counts.get(opt_name, 0) + 1
    
    print(f"\nResults by optimization type:")
    for opt_name in ['Min Variance', 'Max Sharpe', 'Max Sortino', 'Max Omega', 'Max Calmar', 'Max CF Sharpe']:
        count = optimization_counts.get(opt_name, 0)
        status = "✅" if count > 0 else "❌"
        print(f"  {status} {opt_name}: {count}")
    
    maxmod_count = optimization_counts.get('Max CF Sharpe', 0)
    if maxmod_count == 0:
        print(f"\n❌ MaxModSharpe still failing across all test cases!")
        print(f"💡 This suggests a systematic issue in the Rust implementation")
    else:
        print(f"\n✅ MaxModSharpe working with {maxmod_count} results!")

if __name__ == "__main__":
    debug_portfolio_pipeline()
    test_multiple_combos()
