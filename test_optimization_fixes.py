#!/usr/bin/env python3
"""
Test script to validate the Rust optimization fixes
"""

import numpy as np
import pandas as pd
import time
from func_rest import cached_process_combo
from portfolio_optimizer_rust import RUST_OPTIMIZATION_AVAILABLE

def create_test_data():
    """Create test data with known characteristics"""
    np.random.seed(42)  # For reproducible results
    
    # Create 100 periods of data
    periods = 100
    dates = pd.date_range(start='2023-01-01', periods=periods, freq='15T')
    
    # Create 6 test symbols with different return characteristics
    symbols = ['SYM1', 'SYM2', 'SYM3', 'SYM4', 'SYM5', 'SYM6']
    
    # Generate log returns with different characteristics
    data = {}
    
    # SYM1: High positive returns, high volatility
    data['SYM1'] = np.random.normal(0.002, 0.01, periods)
    
    # SYM2: Moderate positive returns, low volatility  
    data['SYM2'] = np.random.normal(0.001, 0.005, periods)
    
    # SYM3: Low positive returns, moderate volatility
    data['SYM3'] = np.random.normal(0.0005, 0.007, periods)
    
    # SYM4: Negative returns (should be filtered post-optimization)
    data['SYM4'] = np.random.normal(-0.001, 0.008, periods)
    
    # SYM5: Mixed returns, high volatility
    data['SYM5'] = np.random.normal(0.0, 0.012, periods)
    
    # SYM6: Low positive returns, very low volatility
    data['SYM6'] = np.random.normal(0.0003, 0.003, periods)
    
    # Create DataFrames
    current_df = pd.DataFrame(data, index=dates)
    historical_df = current_df.copy()
    
    return current_df, historical_df, symbols

def test_no_prefiltering():
    """Test that all combinations are processed without pre-filtering"""
    print("Testing: No pre-filtering of combinations...")
    
    current_df, historical_df, symbols = create_test_data()
    
    # Create combinations that would be filtered in the old system
    # Include combinations with negative mean returns
    test_combos = [
        ('SYM1', 'SYM2', 'SYM3'),  # Good combination
        ('SYM4', 'SYM5', 'SYM6'),  # Combination with negative returns
        ('SYM1', 'SYM4', 'SYM5'),  # Mixed combination
        ('SYM2', 'SYM4', 'SYM6'),  # Another mixed combination
    ]
    
    combo_data = []
    for combo in test_combos:
        combo_data.append((combo, current_df, historical_df))
    
    print(f"  Testing {len(test_combos)} combinations...")
    
    # Run optimization
    start_time = time.time()
    results = cached_process_combo(combo_data)
    end_time = time.time()
    
    print(f"  Processed in {end_time - start_time:.3f} seconds")
    print(f"  Results returned: {len(results)} portfolios")
    
    # Check that we got results for combinations that would have been pre-filtered
    combo_results = {}
    for result_type, candidate in results:
        combo_key = tuple(candidate['combo'])
        if combo_key not in combo_results:
            combo_results[combo_key] = []
        combo_results[combo_key].append(result_type)
    
    print(f"  Combinations processed: {len(combo_results)}")
    for combo, result_types in combo_results.items():
        print(f"    {combo}: {len(result_types)} optimization types")
    
    return len(results) > 0

def test_post_filtering():
    """Test that only negative returns are filtered post-optimization"""
    print("Testing: Post-optimization filtering for negative returns...")
    
    current_df, historical_df, symbols = create_test_data()
    
    # Create a simple combination
    combo_data = [
        (('SYM1', 'SYM2', 'SYM3'), current_df, historical_df)
    ]
    
    results = cached_process_combo(combo_data)
    
    # Check that all returned portfolios have non-negative returns
    negative_return_count = 0
    total_portfolios = 0
    
    for result_type, candidate in results:
        total_portfolios += 1
        portfolio_return = candidate.get('return', 0)
        if portfolio_return < 0:
            negative_return_count += 1
            print(f"    WARNING: Found portfolio with negative return: {portfolio_return}")
    
    print(f"  Total portfolios: {total_portfolios}")
    print(f"  Portfolios with negative returns: {negative_return_count}")
    
    return negative_return_count == 0

def test_optimization_types():
    """Test that all optimization types are working"""
    print("Testing: All optimization types...")
    
    current_df, historical_df, symbols = create_test_data()
    
    combo_data = [
        (('SYM1', 'SYM2', 'SYM3'), current_df, historical_df)
    ]
    
    results = cached_process_combo(combo_data)
    
    # Count optimization types
    optimization_counts = {}
    for result_type, candidate in results:
        opt_type = candidate.get('optimization', result_type)
        optimization_counts[opt_type] = optimization_counts.get(opt_type, 0) + 1
    
    print(f"  Optimization types found:")
    for opt_type, count in optimization_counts.items():
        print(f"    {opt_type}: {count} portfolios")
    
    # Check that we have the main optimization types
    expected_types = ['Min Variance', 'Max Sharpe', 'Max Sortino']
    found_types = set(optimization_counts.keys())
    
    missing_types = []
    for expected in expected_types:
        if not any(expected.lower() in found.lower() for found in found_types):
            missing_types.append(expected)
    
    if missing_types:
        print(f"    WARNING: Missing optimization types: {missing_types}")
        return False
    
    return True

def main():
    """Run all validation tests"""
    print("=" * 60)
    print("RUST OPTIMIZATION VALIDATION TESTS")
    print("=" * 60)
    
    if not RUST_OPTIMIZATION_AVAILABLE:
        print("❌ Rust optimization engine not available!")
        print("   Please ensure the Rust module is compiled and available.")
        return False
    
    print("✅ Rust optimization engine is available")
    print()
    
    # Run tests
    tests = [
        ("No Pre-filtering", test_no_prefiltering),
        ("Post-optimization Filtering", test_post_filtering),
        ("Optimization Types", test_optimization_types),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"  {status}")
        except Exception as e:
            print(f"  ❌ ERROR: {e}")
            results.append((test_name, False))
        print()
    
    # Summary
    print("=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} {test_name}")
    
    print()
    print(f"Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The optimization fixes are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
